import { Link, Text, VisuallyHiddenInput } from "@chakra-ui/react";
import { FormControl, FormErrorMessage, FormLabel } from "@chakra-ui/form-control";
import { useField } from "formik";
import { Flex, Tooltip } from "@chakra-ui/react";
import { TooltipIcon, UploadIcon } from "components/Icons";
import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";
import { Dispatch, SetStateAction, useRef, useState } from "react";
import { CloseIcon } from "@chakra-ui/icons";
import {
	IMAGE_EXTENSION,
	IMAGE_MIME_TYPE,
	IMAGE_VISIBLE_EXTENSION,
	MAX_FILE_SIZE,
	MAX_FILE_SIZE_MB,
	PDF_EXTENSION,
	PDF_MIME_TYPE,
} from "config";
import useAppToast from "hooks/useAppToast";
import { ICrmAttachment } from "interfaces/CrmDocument.interface";
import { toBase64DataUri } from "utils/helpers";

// import ProposalRadioButtonField from "./ProposalRadioButtonField";

interface Props {
	label: string;
	isRequired?: boolean;
	tooltip?: string;
	subtext?: string;
	setDocument: Dispatch<SetStateAction<ICrmAttachment | undefined>>;
	[key: string]: any;
}
const AppealFileUpload = ({
	label,
	isRequired = true,
	tooltip = "",
	subtext = "",
	setDocument,
	...props
}: Props) => {
	//@ts-ignore
	const [field, meta] = useField(props);
	const { locale } = useRouter();
	const { t } = useTranslation(["common", "forms"]);
	const toast = useAppToast();
	const [fileName, setFileName] = useState<string>("");
	const [attachmentId, setAttachmentId] = useState<string>("");
	const handleTranslation = (textToTranslate) => {
		if (
			textToTranslate &&
			typeof textToTranslate === "string" &&
			textToTranslate?.includes("is a required field")
		) {
			let splitData = textToTranslate.split(" ");
			let translatedWord = t(splitData[0], { ns: "forms" });
			return t("isRequired", { ns: "forms", fieldName: translatedWord });
		}
		return t(textToTranslate, { ns: "forms" });
	};
	const hiddenFileInput = useRef<HTMLInputElement>(null);
	const isLoading = false;
	const isDeleting = false;
	const handleClick = () => {
		hiddenFileInput.current?.click();
	};
	const allowedMimeTypes = [
		...(props.allowPdf ? PDF_MIME_TYPE : []),
		...(props.allowImage ? IMAGE_MIME_TYPE : []),
	];
	const allowedExtensions = [
		...(props.allowPdf ? PDF_EXTENSION : []),
		...(props.allowImage ? IMAGE_EXTENSION : []),
	];
	const allowedVisibleExtensions = [
		...(props.allowPdf ? PDF_EXTENSION : []),
		...(props.allowImage ? IMAGE_VISIBLE_EXTENSION : []),
	];
	const handleOnChange = async (event, type?) => {
		const file =
			type === "dragDrop" ? (event.dataTransfer.files[0] as File) : (event.target.files[0] as File);
		if (
			!allowedMimeTypes.includes(file.type) ||
			!allowedExtensions.includes(`.${file.name.toLowerCase().split(".").pop() || ""}`)
		) {
			toast({
				title: t("forms:fileUploadErrorFileTypes", {
					extensions: allowedVisibleExtensions.join(", "),
				}),
				status: "error",
			});
			return;
		}
		if (file.size > MAX_FILE_SIZE) {
			toast({
				title: t("forms:fileUploadErrorFileSize", { size: `${MAX_FILE_SIZE_MB}MB` }),
				status: "error",
			});
			return;
		}
		const myDoc: ICrmAttachment = {
			AttachmentBody: await toBase64DataUri(file),
			FileName: file.name,
			MimeType: file.type,
		};
		setDocument(myDoc);
		setFileName(file.name);
	};
	const handleDrag = function (e) {
		e.preventDefault();
		e.stopPropagation();
	};
	const handleOnDeleteFile = () => {
		setFileName("");
		setDocument(undefined);
	};
	// triggers when file is dropped
	const handleDrop = function (e) {
		e.preventDefault();
		e.stopPropagation();
		if (e.dataTransfer.files && e.dataTransfer.files[0]) {
			handleOnChange(e, "dragDrop");
		}
	};
	return (
		<FormControl
			className="form control"
			isRequired={isRequired}
			isInvalid={Boolean(meta.error && meta.touched)}
			position="relative"
		>
			{label && (
				<Flex alignItems={"center"} mb={2} gap={1}>
					<FormLabel
						fontSize={props.labelFontSize || "sm"}
						color={props.labelColor || "none"}
						requiredIndicator={
							<Text as="span" ml="4px">
								*
							</Text>
						}
						m={0}
						{...field}
					>
						{label}
					</FormLabel>
					{tooltip && (
						<Tooltip
							hasArrow
							label={tooltip}
							placement={locale === "ar" ? "left" : "right"}
							bg={"brand.gray.400"}
							color={"brand.white.50"}
							fontSize={"xs"}
							fontWeight={"normal"}
							arrowSize={8}
							ms={1}
						>
							<TooltipIcon />
						</Tooltip>
					)}
				</Flex>
			)}
			<>
				<VisuallyHiddenInput
					type="file"
					name={props.idDocument}
					ref={hiddenFileInput}
					onChange={handleOnChange}
					multiple={props.multiple}
					required={props.isRequired}
					accept={allowedMimeTypes.join(", ")}
				/>
				<Flex
					bg={"brand.inputColor.formInputBg"}
					w={"100%"}
					alignItems={"center"}
					p={2}
					border={"2px"}
					onDragEnter={handleDrag}
					onDragLeave={handleDrag}
					onDragOver={handleDrag}
					onDrop={handleDrop}
					rounded="lg"
					borderColor={"#DDE1E6"}
				>
					<Flex
						alignItems={"center"}
						cursor="pointer"
						bg={"brand.mainGold"}
						px={5}
						py={2}
						rounded="lg"
						onClick={handleClick}
						minW={"fit-content"}
					>
						<>
							<UploadIcon />
							<Text ms={4} fontSize={"sm"} color={"brand.white.50"} fontWeight={700}>
								{t("forms:selectFiles")}
							</Text>
						</>
					</Flex>
					{fileName && (
						<>
							<Link ms={4} fontSize={"sm"} color={"brand.textColor"}>
								<Text noOfLines={[1]}>{fileName}</Text>
							</Link>
							<Flex
								w={"40px"}
								h={"40px"}
								ms={"auto"}
								justifyContent={"center"}
								alignItems={"center"}
							>
								<CloseIcon ms={"auto"} me={2} onClick={handleOnDeleteFile} />
							</Flex>
						</>
					)}

					{!fileName && (
						<Text ms={4} fontSize={"sm"} color={"brand.textColor"}>
							{t("forms:dropFile")}
						</Text>
					)}
				</Flex>
				<Flex w={"100%"} alignItems={"center"} justifyContent={"space-between"} my={2}>
					<Text fontSize={"xs"} color={"brand.textColor"} dir="ltr">
						{t("forms:filesType", { extensions: allowedVisibleExtensions.join(", ") })}
					</Text>
					<Text fontSize={"xs"} color={"brand.textColor"}>
						{t("forms:maxSize", { size: `${MAX_FILE_SIZE_MB}MB` })}
					</Text>
				</Flex>
			</>

			{subtext && (
				<Text fontSize="sm" fontWeight="normal" color="brand.gray.400">
					{subtext}
				</Text>
			)}
			<FormErrorMessage>
				{Array.isArray(meta.error) ? meta.error[0] : handleTranslation(meta.error)}
			</FormErrorMessage>
		</FormControl>
	);
};
export default AppealFileUpload;
