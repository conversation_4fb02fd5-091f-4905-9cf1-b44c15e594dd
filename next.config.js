/** @type {import('next').NextConfig} */
const { i18n } = require("./next-i18next.config");

const nextConfig = {
	reactStrictMode: true,
	swcMinify: true,
	i18n,
	output: "standalone",
	generateBuildId: async () => {
		// You can, for example, get the latest git commit hash here
		return "HmTWz3cSSSPnOtrHKGKNW";
	},
	// async redirects() {
	// 	return [
	// 		process.env.MAINTENANCE_MODE === "1"
	// 			? {
	// 					source: "/((?!faq)(?!_next)(?!static)(?!assets)(?!fonts).*)",
	// 					destination: "/faq",
	// 					permanent: false,
	// 			  }
	// 			: null,
	// 	].filter(Boolean);
	// },
	// async redirects() {
	// 	return [
	// 		true
	// 			? {
	// 					source: "/((?!under-maintenance)(?!_next)(?!static)(?!assets)(?!fonts).*)",
	// 					destination: "/under-maintenance",
	// 					permanent: false,
	// 			  }
	// 			: null,
	// 	].filter(Boolean);
	// },
	// async redirects() {
	// 	return [
	// 		process.env.NEXT_PUBLIC_IS_OPERATIONS === "true"
	// 			? {
	// 					source: "/complaints/:path*",
	// 					destination: "/smart-services",
	// 					permanent: false,
	// 			  }
	// 			: null,
	// 		{
	// 			source: "/home",
	// 			destination: "/smart-services",
	// 			permanent: false,
	// 		},
	// 	].filter(Boolean);
	// },
	async headers() {
		return [
			{
				source: "/api/:slug*",
				headers: [
					{
						key: "Cache-Control",
						value: "private, no-cache, no-store, max-age=0, must-revalidate",
					},
				],
			},
		];
	},
};

module.exports = nextConfig;
