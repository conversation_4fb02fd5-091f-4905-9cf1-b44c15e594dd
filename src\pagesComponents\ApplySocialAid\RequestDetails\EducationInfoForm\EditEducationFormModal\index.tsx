import {
	Grid,
	Grid<PERSON>tem,
	Modal,
	ModalBody,
	ModalContent,
	ModalHeader,
	ModalOverlay,
	VStack,
	Text,
	Flex,
	HStack,
	Button,
	ModalFooter,
} from "@chakra-ui/react";
import FormField from "components/Form/FormField";
import { CloseIcon } from "components/Icons";
import { useFormContext } from "context/FormContext";
import { Form, Formik, FormikProps } from "formik";
import { IEducationCase } from "interfaces/SocialAidForm.interface";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import React, { useRef } from "react";
import { useMemo } from "react";
import * as functions from "./functions";
import {
	EducationCategoryCurriculumEn,
	EducationCategoryCurriculumAr,
	PublicEducationStreamEn,
	PublicEducationStreamAr,
	ScoresTypeDocumentAr,
	ScoresTypeDocumentEn,
} from "config";
import { getStaticLookupItem } from "utils/helpers";

interface Props {
	onClose: any;
	member: IEducationCase | null;
	onEditMember: (member: IEducationCase) => void;
	readOnly?: boolean;
}

function EditEducationFormModal({ onClose, member, onEditMember, readOnly }: Props) {
	const { t } = useTranslation(["forms", "common"]);
	const { locale } = useRouter();
	const { lookups } = useFormContext();

	const formikRef = useRef<FormikProps<any>>(null);

	const handleChangeEvent = (type, firstArg, secondArg, formik) => {
		if (type === "text") {
			formik.setFieldValue(secondArg, firstArg?.target?.value || "");
		} else if (type === "selectableTags") {
			formik.setFieldValue(secondArg, firstArg);
		} else if (type === "datetime") {
			formik.setFieldValue(secondArg, firstArg);
		}
	};
	const initialValues = useMemo(
		() => ({
			ApplyEducationAllowance: member?.ApplyEducationAllowance === true ? "yes" : "no",
			IsEnrolledInNationalService: member?.IsEnrolledInNationalService === true ? "yes" : "no",
			childCompletedSemesterInUniversity:
				member?.childCompletedSemesterInUniversity === true ? "yes" : "no",
			highSchoolCurriculuim: getStaticLookupItem(
				locale === "en" ? EducationCategoryCurriculumEn : EducationCategoryCurriculumAr,
				member?.highSchoolCurriculuim
			),
			enrolledEducationStream: getStaticLookupItem(
				locale === "en" ? PublicEducationStreamEn : PublicEducationStreamAr,
				member?.enrolledEducationStream
			),
			EmSATorAdvancedPlacementScores: getStaticLookupItem(
				locale === "en" ? ScoresTypeDocumentEn : ScoresTypeDocumentAr,
				member?.EmSATorAdvancedPlacementScores
			),
		}),
		// eslint-disable-next-line react-hooks/exhaustive-deps
		[member?.IdChild, locale]
	);

	const onSubmit = (it) => {
		member &&
			onEditMember({
				...member,
				ApplyEducationAllowance: it?.ApplyEducationAllowance === "yes",
				childCompletedSemesterInUniversity: it?.childCompletedSemesterInUniversity === "yes",
				IsEnrolledInNationalService: it?.IsEnrolledInNationalService === "yes",
				highSchoolCurriculuim: it?.highSchoolCurriculuim?.value,
				enrolledEducationStream: it?.enrolledEducationStream?.value,
				EmSATorAdvancedPlacementScores: it?.EmSATorAdvancedPlacementScores?.value,
				IsCompletedFromPortal: true,
			});
	};

	return (
		<>
			<Modal
				isOpen={!!member}
				onClose={onClose}
				size={{ base: "full", md: "4xl" }}
				isCentered
				scrollBehavior={"inside"}
			>
				<ModalOverlay />
				<ModalContent minW={"45vw"}>
					<ModalHeader>
						<Flex w={"100%"}>
							<CloseIcon ms={"auto"} onClick={onClose} cursor="pointer" />
						</Flex>
						<VStack align={"start"}>
							<Text fontSize={"lg"} fontWeight={"semibold"}>
								{t("editFamilyMembersInformation")}
							</Text>
							<HStack fontSize={"sm"} fontWeight={"semibold"} pt={3}>
								<Text fontSize={"md"} fontWeight={"500"}>
									{locale === "ar" ? member?.FullNameAr : member?.FullNameEn}
								</Text>
							</HStack>
						</VStack>
					</ModalHeader>
					<Formik
						enableReinitialize
						initialValues={initialValues}
						validationSchema={functions.getValidationSchema}
						onSubmit={onSubmit}
						innerRef={formikRef}
					>
						{(formik) => (
							<>
								<ModalBody>
									<Form
										onSubmit={(e) => {
											e.preventDefault();
											formik.handleSubmit(e);
										}}
										onChange={(e) => {
											e.preventDefault();
											functions.onChange(e, formik);
										}}
									>
										<Grid
											rowGap={{ base: 6, md: 6 }}
											columnGap={6}
											templateColumns="repeat(2, 1fr)"
											templateRows="auto"
										>
											<GridItem colSpan={{ base: 2, md: 2 }}>
												<FormField
													type="radio"
													label={t("ApplyEducationAllowance")}
													name="ApplyEducationAllowance"
													value={formik.values["ApplyEducationAllowance"]}
													touched={formik.touched["ApplyEducationAllowance"]}
													error={formik.errors["ApplyEducationAllowance"]}
													options={lookups.Boolean}
													isReadOnly={readOnly}
													onChange={(firstArg) => {
														handleChangeEvent("radio", firstArg, "ApplyEducationAllowance", formik);
														handleChangeEvent("radio", "", "IsEnrolledInNationalService", formik);
														handleChangeEvent(
															"radio",
															"",
															"childCompletedSemesterInUniversity",
															formik
														);
														handleChangeEvent("radio", "", "highSchoolCurriculuim", formik);
														handleChangeEvent("radio", "", "enrolledEducationStream", formik);
														handleChangeEvent(
															"radio",
															"",
															"EmSATorAdvancedPlacementScores",
															formik
														);
													}}
												/>
											</GridItem>
											{formik.values["ApplyEducationAllowance"] === "yes" && (
												<>
													<GridItem colSpan={{ base: 2, md: 2 }}>
														<FormField
															type="radio"
															label={t("IsEnrolledInNationalService")}
															name="IsEnrolledInNationalService"
															value={formik.values["IsEnrolledInNationalService"]}
															touched={formik.touched["IsEnrolledInNationalService"]}
															error={formik.errors["IsEnrolledInNationalService"]}
															options={lookups.Boolean}
															isReadOnly={readOnly}
															onChange={(firstArg) => {
																handleChangeEvent(
																	"radio",
																	firstArg,
																	"IsEnrolledInNationalService",
																	formik
																);
															}}
														/>
														{formik.values["IsEnrolledInNationalService"] === "yes" && (
															<Text color={"red"}>{t("discliamer")}</Text>
														)}
													</GridItem>
													{formik.values["IsEnrolledInNationalService"] === "no" && (
														<GridItem colSpan={{ base: 2, md: 2 }}>
															<Flex>
																<FormField
																	type="radio"
																	label={t("childCompletedSemesterInUniversity")}
																	name="childCompletedSemesterInUniversity"
																	value={formik.values["childCompletedSemesterInUniversity"]}
																	touched={formik.touched["childCompletedSemesterInUniversity"]}
																	error={formik.errors["childCompletedSemesterInUniversity"]}
																	options={lookups.Boolean}
																	isReadOnly={readOnly}
																	onChange={(firstArg) => {
																		handleChangeEvent(
																			"radio",
																			firstArg,
																			"childCompletedSemesterInUniversity",
																			formik
																		);
																	}}
																/>
																<Text
																	fontSize={"sm"}
																	w="90%"
																	color="#1B1D21B8"
																	dangerouslySetInnerHTML={{
																		__html: t("common:accreditedUniversities"),
																	}}
																></Text>
															</Flex>
														</GridItem>
													)}

													{formik.values["childCompletedSemesterInUniversity"] === "no" &&
														formik.values["IsEnrolledInNationalService"] === "no" && (
															<>
																<GridItem colSpan={{ base: 2, md: 1 }}>
																	<FormField
																		type="selectableTags"
																		value={formik.values["highSchoolCurriculuim"]}
																		isRequired={true}
																		name="highSchoolCurriculuim"
																		options={
																			locale === "en"
																				? EducationCategoryCurriculumEn
																				: EducationCategoryCurriculumAr
																		}
																		label={t("highSchoolCurriculuim")}
																		placeholder={t("placeholder", { ns: "common" })}
																		error={formik.errors["highSchoolCurriculuim"]}
																		touched={formik.touched["highSchoolCurriculuim"]}
																		isDisabled={readOnly}
																		onChange={(firstArg) => {
																			handleChangeEvent(
																				"selectableTags",
																				firstArg,
																				"highSchoolCurriculuim",
																				formik
																			);
																		}}
																	/>
																</GridItem>
																{formik.values["highSchoolCurriculuim"] &&
																	formik.values["highSchoolCurriculuim"]?.value &&
																	formik.values["highSchoolCurriculuim"]?.value.toString() ===
																		"662410000" && (
																		<GridItem colSpan={{ base: 2, md: 1 }}>
																			<FormField
																				type="selectableTags"
																				value={formik.values["enrolledEducationStream"]}
																				isRequired={true}
																				name="enrolledEducationStream"
																				options={
																					locale === "en"
																						? PublicEducationStreamEn
																						: PublicEducationStreamAr
																				}
																				label={t("enrolledEducationStream")}
																				placeholder={t("placeholder", { ns: "common" })}
																				error={formik.errors["enrolledEducationStream"]}
																				touched={formik.touched["enrolledEducationStream"]}
																				isDisabled={readOnly}
																				onChange={(firstArg) => {
																					handleChangeEvent(
																						"selectableTags",
																						firstArg,
																						"enrolledEducationStream",
																						formik
																					);
																				}}
																			/>
																		</GridItem>
																	)}
																{formik.values["highSchoolCurriculuim"] &&
																	formik.values["highSchoolCurriculuim"]?.value &&
																	formik.values["highSchoolCurriculuim"]?.value.toString() ===
																		"662410002" && (
																		<GridItem colSpan={{ base: 2, md: 1 }}>
																			<FormField
																				type="selectableTags"
																				value={formik.values["EmSATorAdvancedPlacementScores"]}
																				isRequired={true}
																				name="EmSATorAdvancedPlacementScores"
																				options={
																					locale === "en"
																						? ScoresTypeDocumentEn
																						: ScoresTypeDocumentAr
																				}
																				label={t("EmSATorAdvancedPlacementScores")}
																				placeholder={t("placeholder", { ns: "common" })}
																				error={formik.errors["EmSATorAdvancedPlacementScores"]}
																				touched={formik.touched["EmSATorAdvancedPlacementScores"]}
																				isDisabled={readOnly}
																				onChange={(firstArg) => {
																					handleChangeEvent(
																						"selectableTags",
																						firstArg,
																						"EmSATorAdvancedPlacementScores",
																						formik
																					);
																				}}
																			/>
																		</GridItem>
																	)}
															</>
														)}
												</>
											)}
										</Grid>
									</Form>
								</ModalBody>
								<ModalFooter borderTop="1px solid #BBBCBD">
									<HStack w={"100%"} gap={2} my={4}>
										<Button variant="secondary" w={"100%"} onClick={onClose}>
											{t("common:cancel")}
										</Button>
										<Button
											variant="primary"
											w={"100%"}
											isDisabled={!formik.isValid}
											onClick={formik.submitForm}
										>
											{t("common:save")}
										</Button>
									</HStack>
								</ModalFooter>
							</>
						)}
					</Formik>
				</ModalContent>
			</Modal>
		</>
	);
}

export default EditEducationFormModal;
