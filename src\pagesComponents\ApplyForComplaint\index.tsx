import { <PERSON>ton, Grid, GridI<PERSON>, Box, Text, Flex } from "@chakra-ui/react";
import FormField from "components/Form/FormField";
import { Form, Formik } from "formik";
import { useRouter } from "next/router";
import { useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import * as functions from "./functions";
import { ICrmContact } from "interfaces/CrmContact.interface";
import { ICrmLookupLocalized, ICrmMasterData } from "interfaces/CrmMasterData.interface";
import useAppToast from "hooks/useAppToast";
import { COMPLAINT_TYPE, ComplaintCaseTypes, INQUIRY_TYPE } from "config";
import { ICrmComplaint } from "interfaces/CrmComplaint.interface";
import { useMutation } from "react-query";
import { complaintRequest } from "services/frontend";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import { ICrmAttachment, ICrmDocumentList } from "interfaces/CrmDocument.interface";
import { ICrmRequest } from "interfaces/CrmRequest.interface";
import ComplaintFileUpload from "./ComplaintFileUpload";
import RequestSuccessful from "components/RequestSuccefull";
import { getFormattedDate, getNewLocalizedFullName } from "utils/helpers";
import { ComplaintForm } from "interfaces/CrmComplaintForm.interface";
interface ApplyForComplainFormInterface {
	userDetails: ICrmContact;
	documentList?: ICrmDocumentList;
	setDocumentStatus: any;
	prevRequests: Array<ICrmRequest>;
	masterData: ICrmMasterData<ICrmLookupLocalized>;
	prevComplaints: ComplaintForm[];
	complaintType?: string;
	isAuth?: boolean;
	title?: string;
}
var formObject: ICrmComplaint = {
	EmirateId: "",
	EmailAddress: "",
	PhoneNumber: "",
	Title: "",
	Description: "",
	CaseType: 0,
	listAttachments: [],
	topicId: "",
	serviceId: "",
	subServiceId: "",
	AppealStatus: 0,
};
export default function ApplyForComplainForm({
	userDetails,
	prevRequests,
	masterData,
	prevComplaints,
	complaintType,
	isAuth,
	title,
}: ApplyForComplainFormInterface) {
	const [isInitial, setisInitial] = useState(true);
	const [isSubmitted, setisSubmitted] = useState(false);
	const [requestNumber, setrequestNumber] = useState("");
	const [ticketNumber, setTicketNumber] = useState("");
	const ref = useRef(null);
	const { t } = useTranslation(["personalInfo", "forms", "common", "tables"]);
	const { locale } = useRouter();
	const toast = useAppToast();
	const [attachedDocument, setDocument] = useState<ICrmAttachment>();
	const getInitialData = () => {
		let initValues = { ...functions.getInitialValues };
		initValues.email = userDetails?.PreferredEmail || "";
		initValues.icpMobileNo = userDetails?.PreferredPhoneNumber || "";
		initValues.complaintType = complaintType || "";
		initValues.isAuth = isAuth || false;
		return initValues;
	};
	const [initialValues, setInitialValues] = useState(() => getInitialData());

	const [topic, setTopic] = useState(null);
	const [complaintService, setComplaintService] = useState(null);
	const [complaintSubService, setComplaintSubService] = useState(null);

	const titleName = title ? title + "Header" : "complaintTitle";

	const ComplaintObject = {
		value: ComplaintCaseTypes.Complaint,
		label: t("complaint"),
	};
	const InquiryObject = {
		value: ComplaintCaseTypes.Inquiry,
		label: t("inquiry"),
	};
	const SuggestionObject = {
		value: ComplaintCaseTypes.Suggestion,
		label: t("suggestion"),
	};
	const RequestService = {
		value: ComplaintCaseTypes.RequestService,
		label: t("requestService"),
	};
	// const [casesToComplainAbout, setCases] = useState<Array<ICrmLookupLocalized>>(() => [
	// 	...prevRequests
	// 		.filter((re) => STATUS_SLUG_MAPPING[re.Status.Key] === "requestApproved")
	// 		.map((req) => ({
	// 			value: req.CaseId,
	// 			label: `${req.RequestName} - ${
	// 				masterData.CaseStatus.find((c) => c.value === req.Status.Key)?.label
	// 			}`,
	// 		})),
	// ]);
	const [serviceTypeLookup, setServiceTypeLookup] = useState<Array<ICrmLookupLocalized>>([
		ComplaintObject,
		InquiryObject,
		SuggestionObject,
		RequestService,
	]);

	const handleChangeEvent = (type, firstArg, secondArg, formik, isFieldArray = false) => {
		setisInitial(false);
		if (type === "text" || type === "datetime") {
			handleTextChange(firstArg, secondArg, formik, type);
		} else if (type === "selectableTags" || "radio") {
			handleDropdownChange(firstArg, secondArg, formik, isFieldArray);
		}
	};

	const { mutateAsync: submitComplaintForm, isLoading: submittingFromLoading } = useMutation({
		mutationFn: (complaintObject: ICrmComplaint) => complaintRequest(complaintObject),
		onSuccess: (data: any) => {
			setTicketNumber(data?.Data?.CaseId);
		},
	});

	const handleTextChange = (event, fieldName, formik, type) => {
		formik.setFieldValue(fieldName, type === "datetime" ? event || "" : event?.target?.value || "");
		formik.validateField(fieldName); // Trigger validation after setting the value
	};
	const handleDropdownChange = (value, fieldName, formik, isFieldArray) => {
		formik.setFieldValue(fieldName, value);
	};

	const getComplaintServicesOptions = (reason) => {
		return masterData.ComplaintServices.filter((item) => item.RelatedId === reason.value);
	};

	const getcomplaintSubServicesOptions = (reason) => {
		let filterCode = complaintType === "1" ? "662410001" : "662410000";
		return masterData.ComplaintSubServices.filter(
			(item) =>
				item.RelatedId === reason.value && (item.Code === filterCode || item.Code === "662410002")
		);
	};

	const getTranslatedTopicValue = (currentValue) => {
		return masterData.ComplaintTopics.find((x) => x.value === currentValue?.value);
	};

	const getTranslatedServicesValue = (currentValue) => {
		return masterData.ComplaintServices.find((x) => x.value === currentValue?.value);
	};
	const getTranslatedSubServicesValue = (currentValue) => {
		return masterData.ComplaintSubServices.find((x) => x.value === currentValue?.value);
	};

	const downloadPdf = () => {
		const input = ref.current;
		if (input != null) {
			const style = window.document.createElement("style");
			window.document.head.appendChild(style);

			// Enhanced styling rules for better readability
			style.sheet?.insertRule("body > div:last-child img { display: inline-block; }");
			style.sheet?.insertRule("body > div p { font-size: 14pt !important; font-family: Arial, sans-serif !important; }");
			style.sheet?.insertRule("body div table tbody tr td { font-size: 14pt !important; font-family: Arial, sans-serif !important; }");
			style.sheet?.insertRule("body div table tbody tr td:first-child { font-weight: bold !important; }");
			style.sheet?.insertRule(".chakra-text { font-size: 14pt !important; }");
			style.sheet?.insertRule("h1, h2, h3, h4, h5, h6 { font-size: 16pt !important; font-weight: bold !important; }");
			style.sheet?.insertRule(".myCase-close { display: none; }");
			style.sheet?.insertRule(".download-butn { display: none; }");

			let imgWidth, imgheight, x, y;
			if (screen.width < 768) {
				imgWidth = 384 * 0.5; // Increased from 0.44 to 0.5 for better readability
				imgheight = 645 * 0.45; // Increased from 0.4 to 0.45 for better readability
				x = 15;
				y = 15;
			} else {
				imgWidth = 576 * 0.36; // Increased from 0.32 to 0.36 for better readability
				imgheight = 624 * 0.36; // Increased from 0.32 to 0.36 for better readability
				x = 15;
				y = 30; // Reduced from 40 to 30 to better center the content
			}

			html2canvas(input, {
				scale: 3, // Added scale parameter for better quality
				useCORS: true,
				allowTaint: true,
				logging: false
			}).then((canvas) => {
				style.remove();
				const imgData = canvas.toDataURL("image/png", 1.0); // Added quality parameter
				const pdf = new jsPDF({
					unit: "mm",
					format: "a4",
					compress: true
				});
				pdf.addImage(imgData, "PNG", x, y, imgWidth, imgheight);
				pdf.save(`Case - ${requestNumber}.pdf`);
			});
		}
	};

	return (
		<Formik
			enableReinitialize={true}
			initialValues={initialValues}
			validationSchema={functions.getValidationSchema}
			onSubmit={async (e: any) => {
				let complaintType = "";
				let eid = userDetails?.EmiratesID || "";
				if (e.complaintType === "1") {
					complaintType = "Complaint";
				} else if (e.complaintType === "2") {
					complaintType = "Inquiry";
				}
				if (complaintType === "Complaint" || complaintType === "Inquiry") {
					let existingComplaint: ComplaintForm | null = null;

					if (prevComplaints) {
						existingComplaint =
							prevComplaints.find(
								(complaint) =>
									complaint.Topic.Id === e.Topic.value &&
									complaint.Service.Id === e.complaintServices.value &&
									complaint.SubService.Id === e.complaintSubServices.value &&
									complaint.CaseType === complaintType
							) || null;
					}

					if (existingComplaint !== null && existingComplaint !== undefined) {
						toast({
							status: "error",
							title: t("common:cantCreateComplaint", {
								caseNum: existingComplaint.TicketNumber,
							}),
						});
						return;
					}
				}

				formObject = {
					EmirateId: userDetails?.EmiratesID || "",
					EmailAddress: e.email,
					PhoneNumber: e.mobileNo,
					Title: e.title,
					icpMobileNo: e.icpMobileNo,
					Description: e.complaintDetails,
					CaseType: e.complaintType,
					listAttachments: [],
					topicId: e.Topic.value,
					serviceId: e.complaintServices.value,
					subServiceId: e.complaintSubServices.value,
					IsAuth: isAuth,
					Case: e.caseNumber,
					AppealStatus: 0,
				};
				if (attachedDocument) {
					formObject.listAttachments?.push(attachedDocument);
				}
				let resp = await submitComplaintForm(formObject);
				if (resp.IsSuccess) {
					setisSubmitted(true);
				} else {
					toast({
						title: t("common:genericErrorTitle"),
						description: t("common:genericErrorDescription"),
						status: "error",
					});
				}
			}}
		>
			{(formik) => {
				return (
					<Form
						onSubmit={(e) => {
							e.preventDefault();
							formik.handleSubmit(e);
							formik.setSubmitting(false);
						}}
						onChange={(e) => {
							e.preventDefault();
							functions.onChange(e, formik);
						}}
					>
						{!isSubmitted ? (
							<>
								<Box bg={"brand.white.50"} px={{ base: 4, md: 0 }} pb={8}>
									<Grid
										rowGap={{ base: 2, md: 2 }}
										columnGap={16}
										templateColumns="repeat(2, 1fr)"
										templateRows="auto"
									>
										<GridItem colSpan={{ base: 2, md: 1 }}>
											<FormField
												type="selectableTags"
												value={getTranslatedTopicValue(topic)}
												isRequired={true}
												name={"Topic"}
												label={t("forms:Topic")}
												placeholder={""}
												options={masterData.ComplaintTopics}
												error={formik.errors[`Topic`]}
												onChange={(firstArg) => {
													handleChangeEvent("selectableTags", firstArg, "Topic", formik);
													handleChangeEvent("selectableTags", "", "complaintServices", formik);
													handleChangeEvent("selectableTags", "", "complaintSubServices", formik);
													setTopic(firstArg);
													setComplaintService(null);
													setComplaintSubService(null);
													setTimeout(() => formik.setFieldTouched("Topic", true));
												}}
											/>
										</GridItem>
										{formik.values["Topic"] && (
											<GridItem colSpan={{ base: 2, md: 1 }}>
												<FormField
													type="selectableTags"
													value={getTranslatedServicesValue(complaintService) || ""}
													isRequired={true}
													name={"complaintServices"}
													label={t("complaintServices", { ns: "forms" })}
													placeholder={""}
													options={getComplaintServicesOptions(formik.values["Topic"])}
													error={formik.errors[`complaintServices`]}
													onChange={(firstArg) => {
														handleChangeEvent(
															"selectableTags",
															firstArg,
															"complaintServices",
															formik
														);
														handleChangeEvent("selectableTags", "", "complaintSubServices", formik);
														setComplaintService(firstArg);
														setComplaintSubService(null);
														setTimeout(() => formik.setFieldTouched("complaintServices", true));
													}}
												/>
											</GridItem>
										)}
										{(complaintType === COMPLAINT_TYPE || complaintType === INQUIRY_TYPE) &&
											formik.values["Topic"] &&
											formik.values["complaintServices"] && (
												<GridItem colSpan={{ base: 2, md: 1 }}>
													<FormField
														type="selectableTags"
														value={getTranslatedSubServicesValue(complaintSubService) || ""}
														isRequired={true}
														name={"complaintSubServices"}
														label={t("complaintSubServices", { ns: "forms" })}
														placeholder={""}
														error={formik.errors[`complaintSubServices`]}
														options={getcomplaintSubServicesOptions(
															formik.values["complaintServices"]
														)}
														//touched={formik.touched[`email`]}
														onChange={(firstArg) => {
															handleChangeEvent(
																"selectableTags",
																firstArg,
																"complaintSubServices",
																formik
															);
															setComplaintSubService(firstArg);
															setTimeout(() =>
																formik.setFieldTouched("complaintSubServices", true)
															);
														}}
													/>
												</GridItem>
											)}

										{isAuth && (
											<GridItem colSpan={{ base: 2, md: 1 }}>
												<FormField
													type="text"
													value={formik.values["caseNumber"]}
													isRequired={false}
													name={"caseNumber"}
													label={t("complaintNumber", {
														ns: "personalInfo",
														inquiryType: t(`${title}`),
													})}
													placeholder={""}
													error={formik.errors[`serviceType`]}
													onChange={(firstArg) => {
														handleChangeEvent("text", firstArg, "caseNumber", formik);
													}}
												/>
											</GridItem>
										)}
										<GridItem colSpan={{ base: 2, md: 1 }}>
											<FormField
												type="text"
												value={formik.values["title"]}
												isRequired={true}
												name={"title"}
												label={t(titleName, { ns: "forms" })}
												placeholder={""}
												error={formik.errors[`title`]}
												//touched={formik.touched[`email`]}
												onChange={(firstArg) => {
													handleChangeEvent("text", firstArg, "title", formik);
												}}
											/>
										</GridItem>
										<GridItem colSpan={{ base: 2, md: 1 }}>
											<FormField
												type="text"
												value={formik.values["email"]}
												isRequired={false}
												name={"email"}
												label={t("PreferredEmail", { ns: "forms" })}
												placeholder={""}
												error={formik.errors[`email`]}
												//touched={formik.touched[`email`]}
												onChange={(firstArg) => {
													handleChangeEvent("text", firstArg, "email", formik);
												}}
											/>
										</GridItem>
										{isAuth && (
											<GridItem colSpan={{ base: 2, md: 1 }}>
												<FormField
													type="text"
													value={formik.values["icpMobileNo"]}
													isRequired={false}
													isDisabled={true}
													name={"icpMobileNo"}
													label={t("icpMobileNo")}
													placeholder={""}
													error={formik.errors[`icpMobileNo`]}
													//touched={formik.touched[`mobileNo`]}
													onChange={(firstArg) => {
														handleChangeEvent("text", firstArg, "icpMobileNo", formik);
													}}
												/>
											</GridItem>
										)}
										<GridItem colSpan={{ base: 2, md: 1 }}>
											<FormField
												type="text"
												value={formik.values["mobileNo"]}
												isRequired={true}
												name={"mobileNo"}
												label={t("mobileNo")}
												placeholder={""}
												error={formik.errors[`mobileNo`]}
												//touched={formik.touched[`mobileNo`]}
												onChange={(firstArg) => {
													handleChangeEvent("text", firstArg, "mobileNo", formik);
												}}
											/>
										</GridItem>

										<GridItem bg="white" colSpan={{ base: 2, md: 2 }}>
											<FormField
												type="Textarea"
												value={formik.values["complaintDetails"]}
												isRequired={true}
												name={"complaintDetails"}
												label={t("complaintDetails", {
													ns: "forms",
													inquiryType: t(`forms:${title}`),
												})}
												placeholder={""}
												error={formik.errors[`complaintDetails`]}
												//touched={formik.touched[`email`]}
												onChange={(firstArg) => {
													handleChangeEvent("text", firstArg, "complaintDetails", formik);
												}}
											/>
										</GridItem>
										<GridItem colSpan={{ base: 2, md: 1 }}>
											<ComplaintFileUpload
												multiple={false}
												isRequired={false}
												label={locale === "en" ? "Attachment" : "مستندات اضافية"}
												name={"complaintDocument"}
												allowPdf={true}
												allowImage={true}
												setDocument={setDocument}
											/>
										</GridItem>
										<GridItem></GridItem>
										<GridItem colSpan={{ base: 2, md: 2 }}>
											<Flex justifyContent={"end"}>
												<Button
													mt={5}
													variant="primary"
													type="submit"
													isLoading={submittingFromLoading}
													disabled={
														!formik.isValid ||
														formik.isSubmitting ||
														isInitial ||
														submittingFromLoading
													}
												>
													<Text as="span">{t("submit", { ns: "common" })}</Text>
												</Button>
											</Flex>
										</GridItem>
									</Grid>
								</Box>
							</>
						) : (
							<RequestSuccessful
								userTableData={{
									nameAr: userDetails ? getNewLocalizedFullName(userDetails, locale) : "-",
									nameEn: userDetails ? getNewLocalizedFullName(userDetails, "en") : "-",
									emiratesId: userDetails?.EmiratesID || "-",
									phoneNumber: formObject.PhoneNumber,
									email: formObject.EmailAddress || "-",
								}}
								titles={{
									body: isAuth
										? t("forms:complaintSuccessBody", { inquiryType: t(`${title}`) })
										: t("forms:complaintSuccessBodyForAnonymous", {
												inquiryType: t(`${title}`),
										  }),
									title: t("forms:complaintSuccessTitle", { inquiryType: t(`${title}`) }),
									caseNumberTitle: t("forms:complaintSuccessCaseNumberTitle", {
										inquiryType: t(`${title}`),
									}),
									caseDateTitle: t("forms:complaintSuccessDateTitle", {
										inquiryType: t(`${title}`),
									}),
									tableTitle: t("common:complaintSummaryHead", {
										inquiryType: t(`${title}`),
									}),
								}}
								caseNo={ticketNumber}
								pageName={"complaint"}
								submitDate={getFormattedDate(new Date(), "dd MMMM yyyy", locale)}
							/>
						)}
					</Form>
				);
			}}
		</Formik>
	);
}
