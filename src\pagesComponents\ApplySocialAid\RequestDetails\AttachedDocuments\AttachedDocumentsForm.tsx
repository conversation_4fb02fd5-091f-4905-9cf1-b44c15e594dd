import { <PERSON>rid, GridItem, Text } from "@chakra-ui/react";
import {
	<PERSON><PERSON>,
	<PERSON>lex,
	HStack,
	<PERSON>dal,
	<PERSON>dal<PERSON>ody,
	<PERSON>dal<PERSON>ontent,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON>dal<PERSON>eader,
	ModalOverlay,
	VStack,
	useDisclosure,
} from "@chakra-ui/react";

import FormField from "components/Form/FormField";
import SectionHeader from "components/SectionHeader";
import { Form, Formik } from "formik";
import { ICrmDocumentList } from "interfaces/CrmDocument.interface";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import * as functions from "./functions";
import { PlusSquare, TrashIcon } from "components/Icons";
import { CloseIcon } from "@chakra-ui/icons";
import { useFormContext } from "context/FormContext";
import { useState } from "react";
import { useMutation, useQueryClient } from "react-query";
import { createAdditionalDoc, deleteAdditionalDoc } from "services/frontend";
import useAppToast from "hooks/useAppToast";
import { IWrapperApiResponse } from "interfaces/WrapperApi.interface";

interface Props {
	onSubmit: any;
	documentList?: ICrmDocumentList;
	setDocumentStatus: any;
}

function AttachedDocumentsForm({ onSubmit, documentList, setDocumentStatus }: Props) {
	const { t } = useTranslation(["forms", "common"]);
	const toast = useAppToast();
	const { locale, query } = useRouter();
	const { isOpen, onOpen, onClose } = useDisclosure();
	const { lookups } = useFormContext();
	const queryClient = useQueryClient();
	const [selectedDocType, setSelectedDocType] = useState<any>();
	const [selectedDocId, setSelectedDocId] = useState<any>();
	const createDoc = async () => {
		const resp = await mutateAsync();
		if (resp.IsSuccess) {
			onClose();
			queryClient.refetchQueries(["getDocumentList", query.requestId?.toString()]);
		} else {
			toast({
				title: t("genericErrorTitle", { ns: "common" }),
				description: t("genericErrorDescription", { ns: "common" }),
				status: "error",
			});
		}
		setSelectedDocType(null);
	};
	const { mutateAsync, isLoading } = useMutation("createDoc", () =>
		createAdditionalDoc(selectedDocType.value, query.requestId?.toString() || "")
	);

	const { mutateAsync: deleteMutate, isLoading: isdeleting } = useMutation(deleteAdditionalDoc, {
		onSuccess: (data: IWrapperApiResponse<void>) => {
			if (data.IsSuccess) {
				queryClient.refetchQueries(["getDocumentList", query.requestId?.toString()]);
			} else {
				toast({
					title: t("genericErrorTitle", { ns: "common" }),
					description: t("genericErrorDescription", { ns: "common" }),
					status: "error",
				});
			}
		},
		onError: (error) => {
			toast({
				title: t("genericErrorTitle", { ns: "common" }),
				description: t("genericErrorDescription", { ns: "common" }),
				status: "error",
			});
		},
	});

	return (
		<Formik
			enableReinitialize
			initialValues={functions.getInitialValues}
			validationSchema={functions.getValidationSchema}
			onSubmit={onSubmit}
		>
			{(formik) => (
				<Form
					onSubmit={(e) => {
						e.preventDefault();
						formik.handleSubmit(e);
					}}
					onChange={(e) => {
						e.preventDefault();
						functions.onChange(e, formik);
					}}
				>
					<Grid
						rowGap={{ base: 6, md: 6 }}
						columnGap={6}
						templateColumns="repeat(2, 1fr)"
						templateRows="auto"
					>
						<GridItem bg="white" colSpan={2}>
							<SectionHeader title={t("personalDocuments")} innerText={""} textMt={2} textMb={-2} />
						</GridItem>
						{(documentList?.ListPersonalDocs || []).map((document) => (
							<GridItem bg="white" colSpan={{ base: 2, md: 1 }} key={document.IdDocuments}>
								<FormField
									type="file"
									isRequired={!document.IsOptional}
									label={locale === "en" ? document.NameEn : document.NameAr}
									idDocument={document.IdDocuments}
									name={document.IdDocuments}
									allowPdf={true}
									allowImage={true}
									attachment={document.ListAttachments?.[0]}
									onUploadStatusChange={(status) => {
										setDocumentStatus(document.IdDocuments, status);
									}}
								/>
							</GridItem>
						))}
						{(documentList?.ListAdditionalDoc?.length || 0 > 0) && (
							<GridItem bg="white" colSpan={2}>
								<SectionHeader title={t("additionalDocuments")} textMt={2} textMb={-2} mb={4} />
							</GridItem>
						)}
						{(documentList?.ListAdditionalDoc || []).map((document) => (
							<GridItem bg="white" colSpan={{ base: 2, md: 1 }} key={document.IdDocuments}>
								<HStack>
									<FormField
										type="file"
										isRequired={!document.IsOptional}
										label={locale === "en" ? document.NameEn : document.NameAr}
										idDocument={document.IdDocuments}
										name={document.IdDocuments}
										allowPdf={true}
										allowImage={true}
										attachment={document.ListAttachments?.[0]}
										onUploadStatusChange={(status) => {
											setDocumentStatus(document.IdDocuments, status);
										}}
										style={{ overflow: "hidden", whiteSpace: "nowrap", textOverflow: "ellipsis" }}
									/>
									{document.IsCreatedFromPortal && (
										<Button
											onClick={() => {
												deleteMutate(document.IdDocuments);
											}}
											isLoading={isdeleting}
										>
											<TrashIcon color="red" h="16px" w="14px" transform="scale(1.1,1.1)" />
										</Button>
									)}
								</HStack>
							</GridItem>
						))}
					</Grid>
					{(documentList?.ListAdditionalDoc?.length || 0) === 0 &&
						(documentList?.ListPersonalDocs?.length || 0) === 0 && (
							<Text marginTop={4}>{t("noDocumentsUpload")}</Text>
						)}
					<Button
						variant="secondary"
						p="2px"
						mt={4}
						w={{ base: "100%", md: "auto" }}
						onClick={onOpen}
					>
						<PlusSquare mr={2} transform="scale(1.06,1.06)" />
						<Text fontWeight="bold" textDecor="underline">
							{t("addMoreDocs")}
						</Text>
					</Button>
					<Modal
						isOpen={isOpen}
						onClose={onClose}
						size={{ base: "full", md: "4xl" }}
						isCentered
						scrollBehavior={"inside"}
					>
						<ModalOverlay />
						<ModalContent minW={"45vw"}>
							<ModalHeader>
								<Flex w={"100%"}>
									<CloseIcon
										ms={"auto"}
										onClick={isLoading ? () => {} : onClose}
										cursor="pointer"
									/>
								</Flex>
								<VStack>
									{/* <EditIcon h={"50px"} w={"50px"} /> */}
									<Text fontSize={"lg"}>{t("addMoreDocs")}</Text>
								</VStack>
							</ModalHeader>
							<ModalBody>
								<FormField
									type="selectableTags"
									options={lookups.DocumentProcessTemplate}
									isRequired={true}
									value={selectedDocType}
									name={`docType`}
									label={t("selectDocType")}
									onChange={(firstArg) => {
										setSelectedDocType(firstArg);
									}}
									placeholder=""
								/>
							</ModalBody>
							<ModalFooter>
								<HStack w={"100%"} gap={2} my={4}>
									<Button
										variant="secondary"
										w={"100%"}
										onClick={() => {
											onClose();
											setSelectedDocType(null);
										}}
										isDisabled={isLoading}
									>
										{t("common:cancel")}
									</Button>
									<Button
										variant="primary"
										w={"100%"}
										isDisabled={!selectedDocType || isLoading}
										onClick={createDoc}
										isLoading={isLoading}
									>
										{t("common:save")}
									</Button>
								</HStack>
							</ModalFooter>
						</ModalContent>
					</Modal>
				</Form>
			)}
		</Formik>
	);
}

export default AttachedDocumentsForm;
