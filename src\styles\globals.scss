html,
body {
	padding: 0;
	margin: 0;
	font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell,
		Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
}

a {
	color: inherit;
	text-decoration: none;
}

* {
	box-sizing: border-box;
}

.slider-img .slick-slide {
	padding-right: 40px;
}

.slider-img .slick-slide > div:first-of-type {
	border: 1px solid #BBBCBD;
	border-radius: 10px;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 160px;
}

.slider-img .slick-slide > div:first-of-type:hover {
	border: 1px solid #b08d44;
}

.mycases-dropdown{
	height: 100%;
}

.react-simple-star-rating {
	.ratingMainClass {
		display: inline-block;
		&:hover {
			background: rgba(193, 156, 102, 0.2);
			border-radius: 40px;
		}
	}
}

.stepDev .css-en-15qc96t[aria-current="step"] {
	background: #3e4c62;
	border-color: #3e4c62;
}

.stepDev .css-en-15qc96t[aria-current="step"] span {
	color: #ffff;
}

@media (prefers-color-scheme: dark) {
	html {
		color-scheme: dark;
	}
	body {
		color: white;
		background: black;
	}
}

.custom-modal {
	border-radius: 12px !important;
}

.pagination {
	display: flex;
	list-style: none;
	gap: 5px;
	justify-content: flex-end;
	align-content: center;
	width: 100%;
	margin-top: 24px;
	margin-bottom: 24px;

	&-base {
		justify-content: center;
	}
}

.pagination .break-label,
.page-num,
.links {
	cursor: pointer;
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 4px;
	width: 1.5rem;
	height: 1.5rem;
	padding: 8px;
	font-size: 18px;
	font-weight: 400;
	// line-height: 20px;
}

.pagination .active-page {
	//border: 1px solid #b08d44;
	color:white;
	border-radius: 4px;
	font-style: normal;
	font-weight: 400;
	font-size: 18px;
	line-height: 20px;
	background-color: #b08d44;
	width: 1.5rem;
	height: 1.5rem;
	border-radius: 5000px;
	text-align: center;
	vertical-align: middle;
	display: flex;
	justify-content: center;
	align-items: center;
}

.pagination .disable-link {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 8px;
	background-color: #dde1e6;
	opacity: 0.5;
	border-radius: 4px;
	width: 32px;
	height: 32px;
	color: #121619;
	font-weight: bold;
}


.about-sec{
	height: 65vh;
}

@media (max-height:555px) {
	.about-sec{
		height: unset;
		min-height: 65vh;
	}
}

.overlay::before{
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.6); /* Adjust the opacity here */
	z-index: 1;
}

.root:hover .add-icon{
	color:#b08d44
  }