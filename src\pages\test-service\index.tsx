import {
	<PERSON>,
	<PERSON><PERSON>,
	Flex,
	<PERSON>ing,
	HStack,
	Show,
	Text,
	Link,
	Center,
	Spinner,
} from "@chakra-ui/react";
import MainLayout from "layouts/MainLayout";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import ProgressTracker from "components/ProgressTracker";
import { ReactElement, useEffect, useState } from "react";
import Breadcrumbs from "components/Breadcrumbs";
import { useRouter } from "next/router";
import { LeftArr, SaveIcon } from "components/Icons";
import AccordionSocialAid from "pagesComponents/ApplySocialAid/AccordionSocialAid";
import PersonalInformation from "pagesComponents/ApplySocialAid/RequestDetails/PersonalInformationForm";
import SocialAidInformationForm from "pagesComponents/ApplySocialAid/RequestDetails/SocialAidInformationForm";
import IncomeInformationForm from "pagesComponents/ApplySocialAid/RequestDetails/IncomeInformationForm";
import FamilyMembersInfoForm from "pagesComponents/ApplySocialAid/RequestDetails/FamilyMembersInfoForm";
import ReviewDocument from "pagesComponents/ApplySocialAid/RequestDetails/ReviewDocument";
import { useTranslation } from "next-i18next";
import AttachedDocuments from "pagesComponents/ApplySocialAid/RequestDetails/AttachedDocuments";
//import NextLink from "next/link";
import useAppToast from "hooks/useAppToast";
import useRouterReady from "hooks/useRouterReady";
import { GetServerSidePropsContext, InferGetServerSidePropsType } from "next";
import { BackendServices } from "services/backend";
import { FormContext } from "context/FormContext";
import { initialCrmMasterData } from "interfaces/CrmMasterData.interface";
import {
	addLocalLookups,
	getContactIdFromToken,
	getLocalizedLookups,
	handleApiErrorMessage,
	mapSocialAidFormToCaseForm,
	getEmiratesIdFromToken,
} from "utils/helpers";

import useFamilyMembers from "pagesComponents/ApplySocialAid/RequestDetails/FamilyMembersInfoForm/useFamilyMembers";
import useAttachDocuments from "pagesComponents/ApplySocialAid/RequestDetails/AttachedDocuments/useAttachDocuments";

import useSocialAidRequest from "pagesComponents/ApplySocialAid/useSocialAidRequest";
import { ISocialAidForm } from "interfaces/SocialAidForm.interface";
import useCustomerPulse from "hooks/useCustomerPulse";
import { modifyRequestFromPending } from "services/frontend";
import { CUSTOMER_PULSE_AID_LINKING_ID, CUSTOMER_PULSE_SCRIPT_LINK } from "config";
import Script from "next/script";
import useChildMembers from "pagesComponents/ApplySocialAid/RequestDetails/FamilyMembersInfoForm/useChildMembers";

function ApplySocialAid({
	masterData,
	userDetails,
	formData,
	isRequestPending,
	requestId,
	customerPulseScriptLink,
	customerPulseLinkingId,
	isMartialEmpty,
}: // requiredDocumentsList,
InferGetServerSidePropsType<typeof getServerSideProps>) {
	let tempActiveStep = 0;
	let tempActiveSubStep = 0;
	let tempCurrentStep = 0;
	let hasSSS = false;
	if (formData?.CaseDetails?.CaseRef && formData.CaseDetails.CaseRef.startsWith("SSS")) {
		tempActiveStep = 2;
		tempActiveSubStep = 3;
		tempCurrentStep = 5;
		hasSSS = true;
	}
	if (isRequestPending) {
		tempCurrentStep = 4;
		tempActiveSubStep = 0;
		tempActiveStep = 1;
	}
	const { t } = useTranslation(["forms", "common"]);
	const router = useRouter();
	const toast = useAppToast();
	const routerReady = useRouterReady();
	const { locale, query } = router;
	const [isSubmitingPending, setIsSubmittingPending] = useState(false);
	const [currentStep, setCurrentStep] = useState(tempCurrentStep);
	const [activeStep, setActiveStep] = useState(tempActiveStep);
	const [activeSubIndex, setActiveSubIndex] = useState(tempActiveSubStep);
	const [saveDraftLoading, setSaveDraftLoading] = useState(false);
	const [customerPulseLoading, customerPulseSubmitted, openCustomerPulse] = useCustomerPulse(
		userDetails?.EmiratesID!,
		customerPulseLinkingId!
	);

	const {
		callGetDocumentList,
		documentList,
		setDocumentStatus,
		getDocumentListLoading,
		attachDocumentsStepDisabled,
		isDocumentUploading,
	} = useAttachDocuments(query.requestId?.toString() || "", activeStep === 1);
	const { familyMembers, setFamilyMembers, familyMembersStepDisabled } = useFamilyMembers(
		formData?.CaseDetails,
		activeStep === 0 && activeSubIndex === 3
	);

	const { childMembers, setChildMembers } = useChildMembers(formData?.CaseDetails);

	const [caseForm, setCaseForm] = useState((state) =>
		mapSocialAidFormToCaseForm(state, formData?.CaseDetails, userDetails)
	);
	const [stepFormKey, setStepFormKey] = useState("");
	const [khulasitQaidNumber, setKhulasitQaidNumber] = useState("");
	const steps = [
		{
			label: t("enterRequestDetails"),
			subSteps: [
				t("personalInformation"),
				t("socialAidInformation"),
				t("incomeInformation"),
				t("familyMembersInformation"),
			],
		},
		{ label: t("attachedDocuments"), subSteps: [] },
		{ label: t("reviewDetails"), subSteps: [] },
		{ label: t("submitRequest"), subSteps: [] },
	];
	useEffect(() => {
		let selectedFormKey = "";
		if (currentStep < sectionsArray.length) {
			selectedFormKey = sectionsArray?.[activeSubIndex]?.formKey || "";
		} else if (
			currentStep >= sectionsArray.length &&
			currentStep < sectionsArray.length + documentsArray.length
		) {
			selectedFormKey = documentsArray?.[0]?.formKey || "";
		} else if (currentStep === maxSteps - 1) {
			selectedFormKey = reviewArray?.[0]?.formKey || "";
		}
		if (selectedFormKey !== stepFormKey) setStepFormKey(() => selectedFormKey);
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [activeStep, activeSubIndex]);

	const [buttonState, setFormikState] = useState({
		personalInformation: { isDisabled: true, isLoading: false },
		socialAidInformation: { isDisabled: true, isLoading: false },
		incomeInformation: { isDisabled: false, isLoading: false },
		familyMembersInformation: { isDisabled: false, isLoading: false },
		attachedDocuments: { isDisabled: false, isLoading: false },
		reviewDocuments: { isDisabled: false, isLoading: false },
	});
	useEffect(() => {
		if (isRequestPending) {
			callGetDocumentList();
		}
	}, [isRequestPending]);
	const handleSetFormikState = (newValues, formKey) => {
		if (
			buttonState?.[formKey]?.isDisabled !== newValues.isDisabled ||
			buttonState?.[formKey]?.isLoading !== newValues.isLoading
		) {
			setFormikState((prev) => ({ ...prev, [formKey]: newValues }));
		}
	};
	const handleAddDeleteFieldArray = (action, name, formKey, newObject, id = null) => {
		if (action === "delete") {
			setCaseForm((prev) => {
				let newState = { ...prev };
				newState[formKey][name].splice(id, 1);
				return newState;
			});
		}
	};

	const handleChangeEvent = (type, firstArg, secondArg, formik, formKey, isFieldArray = false) => {
		if (type === "text" || type === "datetime") {
			handleTextChange(firstArg, secondArg, formik, formKey, isFieldArray, type);
		} else if (type === "selectableTags" || "radio") {
			handleDropdownChange(firstArg, secondArg, formik, formKey, isFieldArray);
		}
	};
	const handleTextChange = (event, fieldName, formik, formKey, isFieldArray, type) => {
		setCaseForm((prev) => {
			let newState = { ...prev };
			if (isFieldArray) {
				let [parentFieldName, fieldIndex, fieldNameActual] = fieldName.split(".");
				if (newState[formKey][parentFieldName]?.length !== formik.values[parentFieldName])
					newState[formKey][parentFieldName] = formik.values[parentFieldName].map((val) => {
						let newVal = {};
						Object.keys(val).forEach((key) => {
							if (typeof val[key] === "object") {
								newVal[key] = val[key].value || "";
							} else {
								newVal[key] = val[key];
							}
						});
						return newVal;
					});
				if (
					newState[formKey] &&
					newState[formKey][parentFieldName] &&
					newState[formKey][parentFieldName][fieldIndex]
				) {
					if (type === "datetime") {
						newState[formKey][parentFieldName][fieldIndex][fieldNameActual] = event || "";
					} else {
						newState[formKey][parentFieldName][fieldIndex][fieldNameActual] =
							event?.target?.value || "";
					}
				}
				return newState;
			} else {
				if (newState[formKey]) newState[formKey][fieldName] = event?.target?.value || "";
				return newState;
			}
		});
		formik.setFieldValue(fieldName, type === "datetime" ? event || "" : event?.target?.value || "");
	};
	const handleDropdownChange = (value, fieldName, formik, formKey, isFieldArray) => {
		setCaseForm((prev) => {
			let newState = { ...prev };
			if (isFieldArray) {
				let [parentFieldName, fieldIndex, fieldNameActual] = fieldName.split(".");
				if (
					newState[formKey] &&
					newState[formKey][parentFieldName] &&
					newState[formKey][parentFieldName][fieldIndex]
				)
					newState[formKey][parentFieldName][fieldIndex][fieldNameActual] =
						value?.value || value || "";
				return newState;
			} else {
				if (newState[formKey]) newState[formKey][fieldName] = value?.value || value || "";
				return newState;
			}
		});
		formik.setFieldValue(fieldName, value);
	};
	const breadcrumbsData = [
		{
			label: t("common:navbar-home"),
			id: "navbar-home",
			link: "/",
			isCurrentPage: false,
		},
		{
			label: t("common:navbar-howToApply"),
			id: "navbar-howToApply",
			link: "/smart-services/how-to-apply",
			isCurrentPage: false,
		},
		{
			label: t("common:applayForSocial"),
			id: "howToApplyForSocialAid",
			link: "#",
			isCurrentPage: true,
		},
	];
	let sectionsArray = [
		{
			title: t("personalInformation"),
			formKey: "personalInformation",
			element: (
				<PersonalInformation
					innerText={t("personalInformationSubtext")}
					key="0"
					handleChangeEvent={handleChangeEvent}
					formKey="personalInformation"
					isMaritalInit={caseForm.socialAidInformation.MaritalStatus}
					isMartialEmpty={isMartialEmpty}
					initialData={caseForm.personalInformation}
					handleSetFormikState={handleSetFormikState}
					readOnly={isRequestPending}
				/>
			),
		},
		{
			title: t("socialAidInformation"),
			formKey: "socialAidInformation",
			element: (
				<SocialAidInformationForm
					innerText={t("socialAidInformationSubtext")}
					key="1"
					handleChangeEvent={handleChangeEvent}
					formKey="socialAidInformation"
					initialData={caseForm.socialAidInformation}
					isMaritalInit={caseForm.socialAidInformation.MaritalStatus}
					readOnly={isRequestPending}
					handleSetFormikState={handleSetFormikState}
					isMartialEmpty={isMartialEmpty}
				/>
			),
		},
		{
			title: t("incomeInformation"),
			formKey: "incomeInformation",
			element: (
				<IncomeInformationForm
					innerText={t("incomeInformationSubtext")}
					key="2"
					handleChangeEvent={handleChangeEvent}
					formKey="incomeInformation"
					handleSetFormikState={handleSetFormikState}
					handleAddDeleteFieldArray={handleAddDeleteFieldArray}
					initialData={caseForm.incomeInformation}
					readOnly={isRequestPending}
				/>
			),
		},
		{
			title: t("familyMembersInformation"),
			formKey: "familyMembersInformation",
			element: (
				<FamilyMembersInfoForm
					formKey="familyMembersInformation"
					innerText={t("familyMembersInformationSubtext")}
					key="3"
					khulasitQaidNumber={khulasitQaidNumber}
					familyMembers={familyMembers}
					setFamilyMembers={setFamilyMembers}
					readOnly={isRequestPending}
					childMembers={childMembers}
					setChildMembers={setChildMembers}
				/>
			),
		},
	];
	let documentsArray = [
		{
			title: "",
			formKey: "attachedDocuments",
			element: (
				<AttachedDocuments
					innerText=""
					key="0"
					documentList={documentList}
					setDocumentStatus={setDocumentStatus}
				/>
			),
		},
	];

	let reviewArray = [
		{
			title: t("reviewDetails"),
			formKey: "reviewDetails",
			element: (
				<ReviewDocument
					innerText={t("reviewDetailsSubtext")}
					formKey="reviewDetails"
					setCurrentStep={setCurrentStep}
					handleStepsIndexes={_handleStepsIndexes}
					documentList={documentList}
					familyMembers={familyMembers}
					caseForm={caseForm}
					key="0"
					handleSetFormikState={handleSetFormikState}
					hasSSS={hasSSS}
					childMembers={childMembers}
					educationMembers={[]}
				/>
			),
		},
	];

	const maxSteps = sectionsArray.length + documentsArray.length + reviewArray.length;
	const { updateRequest, updateRequestLoading } = useSocialAidRequest(
		caseForm,
		setCaseForm,
		familyMembers,
		setFamilyMembers,
		activeStep,
		activeSubIndex,
		userDetails,
		childMembers,
		setChildMembers,
		true
	);

	const isRequestPendingSubmit = activeStep === 1 && isRequestPending;
	const handleProceed = async (type) => {
		if (!isRequestPending) {
			if (activeStep !== 1) {
				// if (type === "submit" && !customerPulseSubmitted) {
				// 	openCustomerPulse();
				// 	return;
				// }
				const resp = await updateRequest(type === "submit");
				if (!resp?.Data?.IdCase || !resp?.IsSuccess) {
					handleApiErrorMessage(resp?.Errors, toast, t, locale);
					return;
				}
			}
			if (type === "submit") return; // redirect handled by hook
			if ((activeStep === 0 && activeSubIndex === 3) || activeStep === 1) {
				const { data, isError } = await callGetDocumentList();
				if (!data?.IsSuccess === true || isError) {
					toast({
						title: t("common:genericErrorTitle"),
						description: t("common:genericErrorDescription"),
						status: "error",
					});
					return;
				}
			}
		} else {
			if (activeStep === 0 && activeSubIndex === 3) {
				const { data, isError } = await callGetDocumentList();
				if (!data?.IsSuccess === true || isError) {
					toast({
						title: t("common:genericErrorTitle"),
						description: t("common:genericErrorDescription"),
						status: "error",
					});
					return;
				}
			}
			if (activeStep === 1) {
				setIsSubmittingPending(true);
				const resp = await modifyRequestFromPending(requestId!);
				if (resp.IsSuccess) {
					router.push(
						`/smart-services/how-to-apply/apply-socialaid/edited-socialaid?requestId=${resp?.Data?.IdCase}`
					);
					return;
				}
				setIsSubmittingPending(false);
				if (!resp?.Data?.IdCase || !resp?.IsSuccess) {
					handleApiErrorMessage(resp?.Errors, toast, t, locale);
					return;
				}
			}
		}
		setCurrentStep((currentIndex) => {
			if (currentIndex === maxSteps) {
				_handleStepsIndexes(currentIndex);
				return currentIndex;
			}
			_handleStepsIndexes(currentIndex + 1);
			setTimeout(() => {
				window.scrollTo({
					top: 0,
					behavior: "smooth",
				});
			}, 100);
			return currentIndex + 1;
		});
	};
	const handleBack = () => {
		setCurrentStep((currentIndex) => {
			if (currentIndex === 0) {
				_handleStepsIndexes(currentIndex);
				router.push("/smart-services/how-to-apply");
				return currentIndex;
			}
			_handleStepsIndexes(currentIndex - 1);
			return currentIndex - 1;
		});
	};

	function _handleStepsIndexes(currentIndex) {
		if (currentIndex < sectionsArray.length) {
			setActiveStep(0);
			setActiveSubIndex(currentIndex);
		} else if (
			currentIndex >= sectionsArray.length &&
			currentIndex < sectionsArray.length + documentsArray.length
		) {
			setActiveStep(1);
		} else if (currentIndex === maxSteps - 1) {
			setActiveStep(2);
		}
	}
	// const titles = {
	// 	title: t("requestEditedTitle"),
	// 	caseNumberTitle: t("requestNumber"),
	// 	caseDateTitle: t("forms:complaintSuccessDateTitle"),
	// 	body: t("requestEditedBody1") + t("requestEditedBody2"),
	// 	tableTitle: t("common:applicationSummary"),
	// };
	const onSaveAsDraft = async () => {
		setSaveDraftLoading(true);
		const resp = await updateRequest(false);
		let caseNumber = resp?.Data?.CaseDetails?.CaseRef || caseForm?.personalInformation?.caseID;
		if (resp?.Data?.IdCase) {
			toast({
				title: t("common:draftUpdateSuccess"),
				description: t("common:caseNumberUpdated", { draftId: caseNumber }),
				status: "info",
			});
		} else {
			toast({
				title: t("common:genericErrorTitle"),
				description: t("common:genericErrorDescription"),
				status: "error",
			});
		}
		setSaveDraftLoading(false);
	};

	const proceedButtonLoading =
		!routerReady ||
		updateRequestLoading ||
		isDocumentUploading !== 0 ||
		(((activeStep === 0 && activeSubIndex === 3) || activeStep === 1) && getDocumentListLoading) || // Refetch document list before documents page and review page
		false;

	const showSaveAsDraft = !!query.requestId && !isRequestPending;

	return (
		<Box px={{ base: 0, md: 8 }} py={{ base: 0, md: 8 }} w="100%">
			<Script src={customerPulseScriptLink} strategy="afterInteractive" />
			<Show below={"md"}>
				<HStack my={5} mx={6}>
					<Link onClick={handleBack}>
						<Center>
							<LeftArr
								w={"8px"}
								h={"100%"}
								transform={locale === "ar" ? "scaleX(-1)" : "scaleX(1)"}
							/>
						</Center>
					</Link>

					<Heading
						textAlign="center"
						flexGrow={1}
						size="md"
						fontSize="lg"
						fontWeight="medium"
						p={1}
					>
						{t("applyingForSocialWelfare", { ns: "common" })}
					</Heading>
				</HStack>
			</Show>
			<Box display={{ base: "none", md: "block" }}>
				<Breadcrumbs data={breadcrumbsData} />
			</Box>
			<Flex display={{ base: "none", md: "flex" }}>
				<Heading size="md" fontSize="h4" fontWeight="medium" pb={12.5} flexGrow={1}>
					{t("applyingForSocialWelfare", { ns: "common" })}
				</Heading>
				{/*{showSaveAsDraft && (
					<Box mt={2}>
						 {!saveDraftLoading && (
							<Flex
								cursor="pointer"
								color="brand.mainGold"
								alignItems="center"
								onClick={onSaveAsDraft}
							>
								<>
									<SaveIcon mr={2} />
									<Text fontWeight="bold" textDecor="underline">
										{t("saveAsDraft", { ns: "common" })}
									</Text>
								</>
							</Flex>
						)}
						{saveDraftLoading && <Spinner color="brand.mainGold" />} 
					</Box>
				)}*/}
			</Flex>
			<Box>
				<Flex direction={{ base: "column", md: "row" }}>
					<Box
						minW="263px"
						w={{ base: "100%", md: "auto" }}
						mr={4}
						display={{ base: "block", md: "block" }}
					>
						<ProgressTracker
							activeStep={isRequestPending ? 1 : activeStep}
							activeSubIndex={isRequestPending ? 0 : activeSubIndex}
							steps={steps}
							service="Test"
						/>
					</Box>
					<Box flexGrow={1} bg="unset" boxShadow="unset">
						<FormContext.Provider value={{ lookups: masterData }}>
							<Box mx={0} mb={4}>
								{/* First Section */}
								{currentStep < sectionsArray.length && (
									<AccordionSocialAid currentStep={currentStep} sectionsArray={sectionsArray} />
								)}
								{/* Second Section */}
								{currentStep >= sectionsArray.length &&
									currentStep < sectionsArray.length + documentsArray.length && (
										<AccordionSocialAid
											currentStep={0}
											sectionsArray={documentsArray}
											hideBottom={true}
										/>
									)}
								{/* Third Section */}
								{currentStep === maxSteps - 1 && (
									<AccordionSocialAid currentStep={0} sectionsArray={reviewArray} />
								)}
							</Box>
						</FormContext.Provider>

						<Flex mt={4} justifyContent="space-between">
							<Box display={{ base: "none", md: "block" }}>
								{showSaveAsDraft && (
									<Box mt={2}>
										{!saveDraftLoading && (
											<Flex
												cursor="pointer"
												color="brand.mainGold"
												alignItems="center"
												onClick={onSaveAsDraft}
											>
												<>
													<SaveIcon mr={2} />
													<Text fontWeight="bold" textDecor="underline">
														{t("saveAsDraft", { ns: "common" })}
													</Text>
												</>
											</Flex>
										)}
										{saveDraftLoading && <Spinner color="brand.mainGold" />}
									</Box>
								)}
							</Box>
							<Flex
								flexWrap="wrap"
								flexDir={{ base: "column-reverse", md: "unset" }}
								w={{ base: "100%", md: "auto" }}
								rowGap={{ base: 5, md: "unset" }}
								px={{ base: 5, md: "unset" }}
								pb={{ base: 5, md: "unset" }}
							>
								{showSaveAsDraft && (
									<Box mt={2} w="100%" display={{ base: "block", md: "none" }}>
										{!saveDraftLoading && (
											<Flex
												cursor="pointer"
												color="brand.mainGold"
												alignItems="center"
												justifyContent="center"
												onClick={onSaveAsDraft}
											>
												<>
													<SaveIcon mr={2} />
													<Text fontWeight="bold" textDecor="underline">
														{t("saveAsDraft", { ns: "common" })}
													</Text>
												</>
											</Flex>
										)}
										{saveDraftLoading && (
											<Flex>
												<Spinner mx={{ base: "auto", md: "unset" }} color="brand.mainGold" />
											</Flex>
										)}
									</Box>
								)}
								<Button
									w={{ base: "100%", md: "auto" }}
									variant="secondary"
									me={5}
									isDisabled={currentStep === 0 || hasSSS ? true : false}
									onClick={handleBack}
								>
									{t("back", { ns: "common" })}
								</Button>
								<Button
									w={{ base: "100%", md: "auto" }}
									variant="primary"
									onClick={() => {
										handleProceed(currentStep < maxSteps - 1 ? "proceed" : "submit");
									}}
									isLoading={
										buttonState?.[stepFormKey]?.isLoading ||
										proceedButtonLoading ||
										customerPulseLoading ||
										isSubmitingPending
									}
									disabled={
										buttonState?.[stepFormKey]?.isDisabled ||
										buttonState?.[stepFormKey]?.isLoading ||
										proceedButtonLoading ||
										familyMembersStepDisabled ||
										attachDocumentsStepDisabled ||
										customerPulseLoading ||
										isSubmitingPending
									}
								>
									<Text as="span">
										{t(
											isRequestPendingSubmit
												? "submit"
												: currentStep < maxSteps - 1 || !customerPulseSubmitted
												? "proceed"
												: "submit",
											{
												ns: "common",
											}
										)}
									</Text>
								</Button>
							</Flex>
						</Flex>
					</Box>
				</Flex>
			</Box>
		</Box>
	);
}
export async function getServerSideProps(ctx: GetServerSidePropsContext) {
	const c = (await BackendServices.getMasterData())?.Data || initialCrmMasterData;
	const masterData = addLocalLookups(c);
	const emiratesId = await getEmiratesIdFromToken(ctx.req);
	const profile = await BackendServices.retrieveContact(emiratesId);
	const userDetails = profile.Data;
	const isMartialEmpty =
		profile.Data?.MaritalStatus.MaritalStatusId === "00000000-0000-0000-0000-000000000000";
	const requestId = ctx.query.requestId?.toString();
	let formData: ISocialAidForm | null = null;
	let isRequestPending = !!ctx.query.isPending?.toString();
	// let requiredDocumentsList: ICrmDocumentList | null = null;
	if (!!requestId) {
		const contactId = await getContactIdFromToken(ctx.req);
		formData = (await BackendServices.getRequest(requestId, contactId))?.Data || null;
		if ((!formData || !!formData.SubmissionTime) && !isRequestPending) {
			return {
				redirect: {
					destination: `/${ctx.locale || "ar"}/smart-services/how-to-apply/apply-socialaid`,
					permanent: false,
				},
			};
		}
	}
	if (isRequestPending) {
		if (
			formData?.IdStatus !==
			masterData.CaseStatus.find((c) => c.Name === "Additional Information Required")?.Id
		) {
			return {
				redirect: {
					destination: `/${ctx.locale || "ar"}/my-cases`,
					permanent: false,
				},
			};
		}
	}
	return {
		props: {
			...(await serverSideTranslations(ctx.locale || "ar", ["common", "tables", "forms"])),
			masterData: getLocalizedLookups(masterData, ctx.locale || "ar"),
			userDetails,
			formData,
			isRequestPending,
			requestId: requestId || null,
			customerPulseScriptLink: CUSTOMER_PULSE_SCRIPT_LINK,
			customerPulseLinkingId: CUSTOMER_PULSE_AID_LINKING_ID,
			isMartialEmpty,
		},
	};
}

ApplySocialAid.getLayout = function getLayout(page: ReactElement) {
	return <MainLayout>{page}</MainLayout>;
};
export default ApplySocialAid;
