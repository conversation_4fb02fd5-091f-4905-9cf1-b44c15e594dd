import * as Yup from "yup";
import { FormikHelpers } from "formik/dist/types";
import { ICrmDocumentList } from "interfaces/CrmDocument.interface";
import {
	IChildFamilyMember,
	IEducationCase,
	IFamilyMember,
} from "interfaces/SocialAidForm.interface";
import { getFormattedDate, getLookupLabel, getStaticLookupItem } from "utils/helpers";
import { ICrmMasterData, ICrmLookupLocalized } from "interfaces/CrmMasterData.interface";
import {
	EducationCategoryCurriculumAr,
	EducationCategoryCurriculumEn,
	PublicEducationStreamAr,
	PublicEducationStreamEn,
	ScoresTypeDocumentAr,
	ScoresTypeDocumentEn,
	WIFE_LOOKUP_ID,
} from "config";

const getInitialValues = {
	termsAndConditions: false,
	confirmAccurateInformation: false,
};

const getValidationSchema = () => {
	return Yup.object({
		termsAndConditions: Yup.bool().oneOf([true], "Accept Terms & Conditions is required"),
		confirmAccurateInformation: Yup.bool().oneOf([true], "Please confirm information is accurate"),
	});
};

const onChange = (event: any, formikProps: FormikHelpers<any>) => {
	// console.log(event, formikProps);
};

const createDocumentsTableData = (documents: ICrmDocumentList, locale: string = "ar") => {
	const docsList = [
		...(documents?.ListPersonalDocs || []),
		...(documents?.ListAdditionalDoc || []),
	];
	const tableData = docsList
		.filter((doc) => doc.Status.Value === "Uploaded On Portal")
		.map((doc) => {
			return {
				label: locale === "en" ? doc.NameEn : doc.NameAr,
				value: doc.ListAttachments[0].FileName || "-",
				document: doc.ListAttachments[0],
			};
		});
	return tableData;
};

const createFamilyMembersTablesData = (
	familyMembers: IFamilyMember[],
	childMembers: IChildFamilyMember[],
	lookups: ICrmMasterData<ICrmLookupLocalized>,
	locale?: string,
	formatter?: any,
	educationMembers?: IEducationCase[]
) => {
	const tableData = familyMembers.map((member) => {
		const data = [
			{
				label: "Fullname",
				value: (locale === "ar" ? member.FullnameAR : member.FullnameEN) || "-",
				index: 0,
			},
			{
				label: "relationship",
				value: getLookupLabel(lookups, "FamilyRelationship", member.Relationship) || "-",
				index: 0,
			},
		];

		if (member.IsFamilyMemberContributeToIncome)
			(member.ListIncomeSourceDetails || []).forEach((i, index, arr) => {
				data.push({
					label: "incomeSource",
					value: getLookupLabel(lookups, "IncomeTypes", i.IncomeSource) || "-",
					index: arr.length > 1 ? index + 1 : 0,
				});
				data.push({
					label: "incomeAmount",
					value: prepareFormattedValue(formatter, "incomeAmount", i.IncomeAmount || "-"),
					index: arr.length > 1 ? index + 1 : 0,
				});
				data.push({
					label: "companyName",
					value: i.CompanyName || "-",
					index: arr.length > 1 ? index + 1 : 0,
				});
			});

		if (member.IsFamilyMemberReceivePensionIncome)
			(member.ListPensionDetails || []).forEach((i, index, arr) => {
				data.push({
					label: "PensionType",
					value: getLookupLabel(lookups, "PensionType", i.PensionType) || "-",
					index: arr.length > 1 ? index + 1 : 0,
				});
				data.push({
					label: "PensionAuthority",
					value: getLookupLabel(lookups, "PensionAuthority", i.PensionAuthority) || "-",
					index: arr.length > 1 ? index + 1 : 0,
				});
				data.push({
					label: "pensionAmount",
					value: prepareFormattedValue(formatter, "pensionAmount", i.IncomeAmount || "-"),
					index: arr.length > 1 ? index + 1 : 0,
				});
			});

		if (member.IsFamilyMemberReceiveTradeLicense)
			(member.ListTradeLicenseDetails || []).forEach((i, index, arr) => {
				data.push({
					label: "tradeLicenseAmount",
					value: prepareFormattedValue(formatter, "tradeLicenseAmount", i.IncomeAmount || "-"),
					index: arr.length > 1 ? index + 1 : 0,
				});
			});

		if (member.IsFamilyMemberReceiveRentalIncome)
			(member.ListRentalDetails || []).forEach((i, index, arr) => {
				data.push({
					label: "rentalSource",
					value: getLookupLabel(lookups, "rentalSource", i.RentalSource) || "-",
					index: arr.length > 1 ? index + 1 : 0,
				});
				data.push({
					label: "ContractNo",
					value: i.ContractNumber || "-",
					index: arr.length > 1 ? index + 1 : 0,
				});
				data.push({
					label: "ContractStartDate",
					value: getFormattedDate(i.ContractStartDate, "dd MMMM yyyy", locale) || "-",
					index: arr.length > 1 ? index + 1 : 0,
				});
				data.push({
					label: "ContractEndDate",
					value: getFormattedDate(i.ContractEndDate, "dd MMMM yyyy", locale) || "-",
					index: arr.length > 1 ? index + 1 : 0,
				});
				data.push({
					label: "RentAmount",
					value: prepareFormattedValue(formatter, "RentAmount", i.IncomeAmount || "-"),
					index: arr.length > 1 ? index + 1 : 0,
				});
			});
		if (member.Relationship !== WIFE_LOOKUP_ID) {
			const isH = childMembers.find((m) => m.Id === member.Id)?.IsPursuingHigherEducation;
			const isMilt = childMembers.find((m) => m.Id === member.Id)?.IsDraftedinMilitaryService;
			const ocupation = childMembers.find((m) => m.Id === member.Id)?.Occupations;
			data.push({
				label: "IsPursuingHigherEducation",
				value: getLookupLabel(lookups, "Boolean", isH ? "yes" : "no"),
				index: 0,
			});
			data.push({
				label: "IsDraftedinMilitaryService",
				value: getLookupLabel(lookups, "Boolean", isMilt ? "yes" : "no"),
				index: 0,
			});
			data.push({
				label: "Occupations",
				value: getLookupLabel(lookups, "Occupations", ocupation),
				index: 0,
			});
			const educationChild = educationMembers?.find(
				(m) => m.IdChild === member.IdDependentBeneficary
			);

			if (educationChild) {
				data.push({
					label: "ApplyEducationAllowance",
					value: getLookupLabel(
						lookups,
						"Boolean",
						educationChild?.ApplyEducationAllowance ? "yes" : "no"
					),
					index: 0,
				});
				data.push({
					label: "IsEnrolledInNationalService",
					value: getLookupLabel(
						lookups,
						"Boolean",
						educationChild?.IsEnrolledInNationalService ? "yes" : "no"
					),
					index: 0,
				});
				data.push({
					label: "childCompletedSemesterInUniversity",
					value: getLookupLabel(
						lookups,
						"Boolean",
						educationChild?.childCompletedSemesterInUniversity ? "yes" : "no"
					),
					index: 0,
				});

				if (
					!educationChild?.childCompletedSemesterInUniversity &&
					educationChild?.highSchoolCurriculuim
				) {
					data.push({
						label: "highSchoolCurriculuim",
						value: getStaticLookupItem(
							locale === "en" ? EducationCategoryCurriculumEn : EducationCategoryCurriculumAr,
							educationChild?.highSchoolCurriculuim
						)?.label,
						index: 0,
					});
				}

				if (educationChild?.highSchoolCurriculuim?.toString() === "662410000") {
					data.push({
						label: "enrolledEducationStream",
						value: getStaticLookupItem(
							locale === "en" ? PublicEducationStreamEn : PublicEducationStreamAr,
							educationChild?.enrolledEducationStream
						)?.label,
						index: 0,
					});
				}
				if (educationChild?.highSchoolCurriculuim?.toString() === "662410002") {
					data.push({
						label: "EmSATorAdvancedPlacementScores",
						value: getStaticLookupItem(
							locale === "en" ? ScoresTypeDocumentEn : ScoresTypeDocumentAr,
							educationChild?.EmSATorAdvancedPlacementScores
						)?.label,
						index: 0,
					});
				}
			}
		} else {
			const ocupation = familyMembers.find((m) => m.Id === member.Id)?.Occupations;
			data.push({
				label: "Occupations",
				value: getLookupLabel(lookups, "Occupations", ocupation),
				index: 0,
			});
		}
		return { data, isWife: member.Relationship === WIFE_LOOKUP_ID };
	});
	return tableData;
};
const createEducationTablesData = (
	familyMembers: IFamilyMember[],
	lookups: ICrmMasterData<ICrmLookupLocalized>,
	locale?: string,
	educationMembers?: IEducationCase[]
) => {
	/*
	filtering only the education family members...
	...and copying both object properties in the same object for later use below
	*/
	const educationFamilyMembers = familyMembers
		?.map((memebr) => {
			const educationMember = educationMembers?.find(
				(memeber) => memeber.IdChild === memebr.IdDependentBeneficary
			);

			if (educationMember) {
				return {
					...memebr,
					...educationMember,
				};
			}
		})
		.filter((item) => item !== undefined);

	const tableData = educationFamilyMembers?.map((educationChild) => {
		const data = [
			{
				label: "Fullname",
				value: locale === "ar" ? educationChild?.FullnameAR : educationChild?.FullnameEN || "-",
				index: 0,
			},
			{
				label: "relationship",
				value: getLookupLabel(lookups, "FamilyRelationship", educationChild?.Relationship) || "-",
				index: 0,
			},
		];

		// if (educationChild) {
		data.push({
			label: "ApplyEducationAllowance",
			value: getLookupLabel(
				lookups,
				"Boolean",
				educationChild?.ApplyEducationAllowance ? "yes" : "no"
			),
			index: 0,
		});
		data.push({
			label: "IsEnrolledInNationalService",
			value: getLookupLabel(
				lookups,
				"Boolean",
				educationChild?.IsEnrolledInNationalService ? "yes" : "no"
			),
			index: 0,
		});
		data.push({
			label: "childCompletedSemesterInUniversity",
			value: getLookupLabel(
				lookups,
				"Boolean",
				educationChild?.childCompletedSemesterInUniversity ? "yes" : "no"
			),
			index: 0,
		});

		if (
			!educationChild?.childCompletedSemesterInUniversity &&
			educationChild?.highSchoolCurriculuim
		) {
			data.push({
				label: "highSchoolCurriculuim",
				value: getStaticLookupItem(
					locale === "en" ? EducationCategoryCurriculumEn : EducationCategoryCurriculumAr,
					educationChild?.highSchoolCurriculuim
				)?.label,
				index: 0,
			});
		}

		if (educationChild?.highSchoolCurriculuim?.toString() === "662410000") {
			data.push({
				label: "enrolledEducationStream",
				value: getStaticLookupItem(
					locale === "en" ? PublicEducationStreamEn : PublicEducationStreamAr,
					educationChild?.enrolledEducationStream
				)?.label,
				index: 0,
			});
		}
		if (educationChild?.highSchoolCurriculuim?.toString() === "662410002") {
			data.push({
				label: "EmSATorAdvancedPlacementScores",
				value: getStaticLookupItem(
					locale === "en" ? ScoresTypeDocumentEn : ScoresTypeDocumentAr,
					educationChild?.EmSATorAdvancedPlacementScores
				)?.label,
				index: 0,
			});
		}
		// }
		return { data };
	});
	return tableData;
};

const createIncomeTablesData = (
	incomeData: any,
	lookups: ICrmMasterData<ICrmLookupLocalized>,
	locale?: string,
	formatter?: any
) => {
	const tables = {
		tradeLicense: [] as any[],
		pension: [] as any[],
		income: [] as any[],
		RentalIncomes: [] as any[],
	};

	if (incomeData.householdHeadContributes === "yes")
		(incomeData.incomes || [])
			.filter((i) => !!i.incomeAmount)
			.forEach((i, index, arr) => {
				tables.income.push({
					label: "incomeSource",
					value: getLookupLabel(lookups, "IncomeTypes", i.IncomeTypes) || "-",
					index: arr.length > 1 ? index + 1 : 0,
				});
				tables.income.push({
					label: "incomeAmount",
					value: prepareFormattedValue(formatter, "incomeAmount", i.incomeAmount || "-"),
					index: arr.length > 1 ? index + 1 : 0,
				});
				tables.income.push({
					label: "companyName",
					value: i.companyName || "-",
					index: arr.length > 1 ? index + 1 : 0,
				});
			});

	if (incomeData.householdHeadPension === "yes")
		(incomeData.pensions || [])
			.filter((i) => !!i.pensionAmount)
			.forEach((i, index, arr) => {
				tables.pension.push({
					label: "PensionType",
					value: getLookupLabel(lookups, "PensionType", i.PensionType) || "-",
					index: arr.length > 1 ? index + 1 : 0,
				});
				tables.pension.push({
					label: "PensionAuthority",
					value: getLookupLabel(lookups, "PensionAuthority", i.PensionAuthority) || "-",
					index: arr.length > 1 ? index + 1 : 0,
				});
				tables.pension.push({
					label: "pensionAmount",
					value: prepareFormattedValue(formatter, "pensionAmount", i.pensionAmount || "-"),
					index: arr.length > 1 ? index + 1 : 0,
				});
			});

	if (incomeData.householdHeadTradeLicense === "yes")
		(incomeData.tradeLicenses || [])
			.filter((i) => !!i.tradeLicenseAmount)
			.forEach((i, index, arr) => {
				tables.tradeLicense.push({
					label: "tradeLicenseAmount",
					value: prepareFormattedValue(
						formatter,
						"tradeLicenseAmount",
						i.tradeLicenseAmount || "-"
					),
					index: arr.length > 1 ? index + 1 : 0,
				});
			});

	if (incomeData.householdRentalIncomes === "yes")
		(incomeData.RentalIncomes || [])
			.filter((i) => !!i.RentAmount)
			.forEach((i, index, arr) => {
				tables.RentalIncomes.push({
					label: "rentalSource",
					value: getLookupLabel(lookups, "rentalSource", i.rentalSource) || "-",
					index: arr.length > 1 ? index + 1 : 0,
				});
				tables.RentalIncomes.push({
					label: "ContractNo",
					value: i.ContractNo || "-",
					index: arr.length > 1 ? index + 1 : 0,
				});
				tables.RentalIncomes.push({
					label: "ContractStartDate",
					value: getFormattedDate(i.ContractStartDate, "dd MMMM yyyy", locale) || "-",
					index: arr.length > 1 ? index + 1 : 0,
				});
				tables.RentalIncomes.push({
					label: "ContractEndDate",
					value: getFormattedDate(i.ContractEndDate, "dd MMMM yyyy", locale) || "-",
					index: arr.length > 1 ? index + 1 : 0,
				});
				tables.RentalIncomes.push({
					label: "RentAmount",
					value: prepareFormattedValue(formatter, "RentAmount", i.RentAmount || "-"),
					index: arr.length > 1 ? index + 1 : 0,
				});
			});

	return tables;
};

export {
	getInitialValues,
	onChange,
	getValidationSchema,
	createDocumentsTableData,
	createFamilyMembersTablesData,
	createEducationTablesData,
	createIncomeTablesData,
};

function prepareFormattedValue(formatter, fieldName, fieldValue) {
	if (fieldValue === "-" || !formatter || !fieldValue || !(fieldName in formatter))
		return fieldValue;
	try {
		return formatter[fieldName](fieldValue) || fieldValue;
	} catch {
		return fieldValue;
	}
}
