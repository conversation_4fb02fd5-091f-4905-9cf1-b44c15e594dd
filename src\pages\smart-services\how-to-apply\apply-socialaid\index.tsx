import {
	Box,
	Button,
	Flex,
	<PERSON>dal,
	<PERSON>dal<PERSON>ody,
	ModalClose<PERSON>utton,
	<PERSON>dal<PERSON>ontent,
	ModalHeader,
	ModalOverlay,
	Text,
	useDisclosure,
} from "@chakra-ui/react";
import MainLayout from "layouts/MainLayout";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import ProgressTracker from "components/ProgressTracker";
import { ReactElement, useEffect, useState, useRef } from "react";
import { useRouter } from "next/router";
import AccordionSocialAid from "pagesComponents/ApplySocialAid/AccordionSocialAid";
import PersonalInformation from "pagesComponents/ApplySocialAid/RequestDetails/PersonalInformationForm";
import SocialAidInformationForm from "pagesComponents/ApplySocialAid/RequestDetails/SocialAidInformationForm";
import HousingInfoForm from "pagesComponents/ApplySocialAid/RequestDetails/HousingInfoForm";
import FamilyMembersInfoForm from "pagesComponents/ApplySocialAid/RequestDetails/FamilyMembersInfoForm";
import ReviewDocument from "pagesComponents/ApplySocialAid/RequestDetails/ReviewDocument";
import { useTranslation } from "next-i18next";
import AttachedDocuments from "pagesComponents/ApplySocialAid/RequestDetails/AttachedDocuments";
//import NextLink from "next/link";
import useAppToast from "hooks/useAppToast";
import useRouterReady from "hooks/useRouterReady";
import { GetServerSidePropsContext, InferGetServerSidePropsType } from "next";
import { BackendServices } from "services/backend";
import { FormContext } from "context/FormContext";
import { initialCrmMasterData } from "interfaces/CrmMasterData.interface";
import {
	addLocalLookups,
	getContactIdFromToken,
	getLocalizedLookups,
	handleApiErrorMessage,
	mapSocialAidFormToCaseForm,
	getEmiratesIdFromToken,
	getIsEmiratesIDExpiryDateFromToken,
	getIsEmiratesNationalityFromToken,
} from "utils/helpers";

import useFamilyMembers from "pagesComponents/ApplySocialAid/RequestDetails/FamilyMembersInfoForm/useFamilyMembers";
import useAttachDocuments from "pagesComponents/ApplySocialAid/RequestDetails/AttachedDocuments/useAttachDocuments";

import useSocialAidRequest from "pagesComponents/ApplySocialAid/useSocialAidRequest";
import { ISocialAidForm } from "interfaces/SocialAidForm.interface";
import useCustomerPulse from "hooks/useCustomerPulse";
import {
	generateOtp,
	modifyRequestFromPending,
	validateGuardianOtp,
	validateAccount,
} from "services/frontend";
import {
	BORN_UNKNOWN_PARENTS,
	CUSTOMER_PULSE_AID_LINKING_ID,
	CUSTOMER_PULSE_SCRIPT_LINK,
} from "config";
import Script from "next/script";
import useChildMembers from "pagesComponents/ApplySocialAid/RequestDetails/FamilyMembersInfoForm/useChildMembers";
import IncomeFamilyDetails from "pagesComponents/ApplySocialAid/RequestDetails/IncomeFamilyDetails";
import ValidateOtp from "pagesComponents/ValidateOtp";
import { useMutation } from "react-query";
import EducationInfoForm from "pagesComponents/ApplySocialAid/RequestDetails/EducationInfoForm";
import useEducationMembers from "pagesComponents/ApplySocialAid/RequestDetails/EducationInfoForm/useEducationMembers";
import InflationInfoForm from "pagesComponents/ApplySocialAid/RequestDetails/InflationInfoForm";
import { ICrmAccountData } from "interfaces/CrmAccountData.interface";

function ApplySocialAid({
	masterData,
	userDetails,
	formData,
	isRequestPending,
	requestId,
	customerPulseScriptLink,
	customerPulseLinkingId,
	isMartialEmpty,
	hasSubmitted,
	incomeData,
	guardianEId,
	isEidExp,
	isEmirates,
	applyforAllowance,
	eligibleHousing,
	eligibleEducation,
	IsEdit,
	eligibleInflation,
	inflationCaseNumber,
	isHousingEducationTopup,
}: // requiredDocumentsList,
InferGetServerSidePropsType<typeof getServerSideProps>) {
	let tempActiveStep = 0;
	let tempActiveSubStep = 0;
	let tempCurrentStep = 0;
	let hasSSS = false;
	if (formData?.CaseDetails?.CaseRef && formData.CaseDetails.CaseRef.startsWith("SSS")) {
		tempActiveStep = 2;
		tempActiveSubStep = 3;
		tempCurrentStep = 5;
		hasSSS = true;
	}
	if (applyforAllowance) {
		tempActiveStep = 0;
		tempActiveSubStep = 1;
		tempCurrentStep = 1;
	} else if (hasSubmitted) {
		tempActiveStep = 0;
		tempActiveSubStep = 6;
		tempCurrentStep = 6;
	}
	// if (isRequestPending) {
	// 	tempCurrentStep = 4;
	// 	tempActiveSubStep = 0;
	// 	tempActiveStep = 1;
	// }
	const { t } = useTranslation(["forms", "common", "about", "login"]);
	const router = useRouter();
	const toast = useAppToast();
	const { isOpen, onOpen, onClose } = useDisclosure();
	const routerReady = useRouterReady();
	const { locale, query } = router;
	const [userDetailsState, setUserDetailsState] = useState(() => userDetails);
	const [isSubmitingPending, setIsSubmittingPending] = useState(false);
	const [isFormReadOnly, setIsFormReadOnly] = useState(false);
	const [isGuardianVerified, setIsGuardianVerified] = useState(() =>
		guardianEId !== "" ? true : false
	);
	const verifiedGuardianEId = useRef(guardianEId);
	const submitButton: any = useRef();
	const username: any = useRef("");
	const caseSavedCategory: any = useRef(formData?.CaseDetails?.Category);
	const caseSavedSubCategory: any = useRef(formData?.CaseDetails?.SubCategory);
	const reInitializePeronalForm: any = useRef("");

	const [otp, setOtp] = useState("");
	const flag = useRef(false);
	const verifyGaurdianPressed = useRef(false);
	const isCategoryChange = useRef(false);
	const [otpNumber, setOtpNumber] = useState("");
	const [otpError, setOtpError] = useState(false);
	const [encryptedMobileNumber, setEncryptedMobileNumber] = useState("");
	const [currentStep, setCurrentStep] = useState(tempCurrentStep);
	const [activeStep, setActiveStep] = useState(tempActiveStep);
	const [activeSubIndex, setActiveSubIndex] = useState(tempActiveSubStep);
	const [saveDraftLoading, setSaveDraftLoading] = useState(false);
	const [customerPulseLoading, customerPulseSubmitted, openCustomerPulse] = useCustomerPulse(
		userDetailsState?.EmiratesID!,
		customerPulseLinkingId!
	);

	const { mutateAsync: callGenerateOtp, isLoading: generateOtpLoading } = useMutation({
		mutationFn: () => generateOtp(username.current),
		mutationKey: "generateOtp",
	});

	const { mutateAsync: callValidateGuardianOtp, isLoading: signInLoading } = useMutation({
		mutationFn: () => validateGuardianOtp(username.current, otp, encryptedMobileNumber),
		mutationKey: "validateGuardianOtp",
	});

	const { mutateAsync: callValidateAccount, isLoading: isValidatingAccount } = useMutation({
		mutationFn: () =>
			validateAccount(
				caseForm.inflationInformation.UtilityAccountNumber,
				caseForm.inflationInformation.UtilityProvider,
				query.requestId?.toString() ? query.requestId?.toString() : "",
				formData?.ParentCaseId ? formData?.ParentCaseId : "********-0000-0000-0000-********0000",
				""
			),
		mutationKey: "validateAccount",
	});

	const onEnterOtp = (otp) => {
		setOtpError(false);
		setOtp(otp);
	};

	useEffect(() => {
		if (flag.current) {
			setCaseForm((state) => mapSocialAidFormToCaseForm(state, caseForm, userDetailsState));
			if (activeSubIndex === 0 && verifyGaurdianPressed.current) {
				verifyGaurdianPressed.current = false;
				verifiedGuardianEId.current = caseForm.socialAidInformation.GuardianEmiratesID;
				handleProceed(currentStep < maxSteps - 1 ? "proceed" : "submit");
				reInitializePeronalForm.current = reInitializePeronalForm.current + "1";
			}
			flag.current = false;
		}
	}, [userDetailsState]);

	const onValidateOtp = async () => {
		if (!otp || otp.length !== 6) {
			setOtpError(true);
			return;
		}
		const res = await callValidateGuardianOtp();

		if (!res.IsSuccess || !res.Data) {
			handleApiErrorMessage(res?.Errors, toast, t, locale);
		}

		if (res?.IsSuccess) {
			flag.current = true;
			await setUserDetailsState(res?.Data?.retrieveContactResponse);
			onClose();
		}
	};

	const onCancelOtp = () => {
		onClose();
	};

	const onRequestOtp = async () => {
		setOtpError(false);
		const data = await callGenerateOtp();

		if (data?.IsSuccess) {
			setOtpNumber(data?.Data?.MobileNumber || "XXXXXXX");
			setEncryptedMobileNumber(data?.Data?.EncryptedMobileNumber || "");
			onOpen();
		} else {
			handleApiErrorMessage(data?.Errors, toast, t, locale);
		}
	};

	const {
		callGetDocumentList,
		documentList,
		setDocumentStatus,
		getDocumentListLoading,
		attachDocumentsStepDisabled,
		isDocumentUploading,
	} = useAttachDocuments(query.requestId?.toString() || "", activeStep === 1);
	const { familyMembers, setFamilyMembers, familyMembersStepDisabled } = useFamilyMembers(
		formData?.CaseDetails,
		activeStep === 0 && activeSubIndex === 2,
		hasSubmitted
	);

	const { educationMembers, setEducationyMembers, educationStepDisabled } = useEducationMembers(
		formData?.CaseDetails,
		activeStep === 0 && activeSubIndex === 4
	);

	const { childMembers, setChildMembers } = useChildMembers(formData?.CaseDetails);
	const [khulasitQaidNumber, setKhulasitQaidNumber] = useState("");

	// Define isEditChildCase similar to inflation service
	let isEditChildCase =
		formData?.ParentCaseId && formData?.ParentCaseId !== "********-0000-0000-0000-********0000"
			? true
			: false;

	const [caseForm, setCaseForm] = useState((state) =>
		mapSocialAidFormToCaseForm(state, formData?.CaseDetails, userDetailsState)
	);

	const [stepFormKey, setStepFormKey] = useState("");
	const steps = [
		{
			label: t("enterRequestDetails"),
			subSteps: [
				t("socialAidInformation"),
				t("personalInformation"),
				t("familyMembersInformation"),
				t("systemValidation"),
				t("documentGeneration"),
				t("housingInformation"),
				t("educationInformation"),
				t("inflationInformation"),
				t("incomeInformation"),
			],
		},
		{ label: t("attachedDocuments"), subSteps: [] },
		{ label: t("reviewDetails"), subSteps: [] },
		{ label: t("submitRequest"), subSteps: [] },
	];
	const newBeneficiarySteps = [
		{
			label: t("enterRequestDetails"),
			subSteps: [
				t("socialAidInformation"),
				t("personalInformation"),
				t("familyMembersInformation"),
				t("systemValidation"),
				t("documentGeneration"),
				t("housingInformation"),
				t("educationInformation"),
				t("inflationInformation"),
			],
		},
		{ label: t("attachedDocuments"), subSteps: [] },
		{ label: t("reviewDetails"), subSteps: [] },
		{ label: t("submitRequest"), subSteps: [] },
	];
	useEffect(() => {
		let selectedFormKey = "";
		if (currentStep < sectionsArray.length) {
			selectedFormKey = sectionsArray?.[activeSubIndex]?.formKey || "";
		} else if (
			currentStep >= sectionsArray.length &&
			currentStep < sectionsArray.length + documentsArray.length
		) {
			selectedFormKey = documentsArray?.[0]?.formKey || "";
		} else if (currentStep === maxSteps - 1) {
			selectedFormKey = reviewArray?.[0]?.formKey || "";
		}
		if (selectedFormKey !== stepFormKey) setStepFormKey(() => selectedFormKey);
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [activeStep, activeSubIndex]);

	useEffect(() => {
		if (isEidExp) {
			toast({
				title: t("common:genericErrorTitle"),
				description: t("common:expEidDate"),
				status: "error",
			});
		} else if (!isEmirates) {
			toast({
				title: t("common:genericErrorTitle"),
				description: t("common:notEmarati"),
				status: "error",
			});
		}

		if (applyforAllowance) {
			if (activeStep === 0 && activeSubIndex === 1) {
				setTimeout(() => {
					submitButton.current.click();
				}, 1000);
			}
		}
	}, []);

	const [buttonState, setFormikState] = useState({
		personalInformation: { isDisabled: true, isLoading: false },
		socialAidInformation: { isDisabled: true, isLoading: false },
		housingInformation: { isDisabled: false, isLoading: false },
		inflationInformation: { isDisabled: false, isLoading: false },
		educationInformation: { isDisabled: false, isLoading: false },
		incomeInformation: { isDisabled: false, isLoading: false },
		familyMembersInformation: { isDisabled: false, isLoading: false },
		attachedDocuments: { isDisabled: false, isLoading: false },
		reviewDocuments: { isDisabled: false, isLoading: false },
	});
	useEffect(() => {
		if (activeStep === 1 && !isRequestPending) {
			callGetDocumentList();
		}
	}, [activeStep]);

	const handleSetFormikState = (newValues, formKey) => {
		if (
			buttonState?.[formKey]?.isDisabled !== newValues.isDisabled ||
			buttonState?.[formKey]?.isLoading !== newValues.isLoading
		) {
			setFormikState((prev) => ({ ...prev, [formKey]: newValues }));
		}
	};
	const handleAddDeleteFieldArray = (action, name, formKey, newObject, id = null) => {
		if (action === "delete") {
			setCaseForm((prev) => {
				let newState = { ...prev };
				newState[formKey][name].splice(id, 1);
				return newState;
			});
		}
	};

	const handleChangeEvent = (type, firstArg, secondArg, formik, formKey, isFieldArray = false) => {
		if (type === "text" || type === "datetime") {
			handleTextChange(firstArg, secondArg, formik, formKey, isFieldArray, type);
		} else if (type === "selectableTags" || "radio") {
			handleDropdownChange(firstArg, secondArg, formik, formKey, isFieldArray);
		}
	};
	const handleTextChange = (event, fieldName, formik, formKey, isFieldArray, type) => {
		setCaseForm((prev) => {
			let newState = { ...prev };
			if (isFieldArray) {
				let [parentFieldName, fieldIndex, fieldNameActual] = fieldName.split(".");
				if (newState[formKey][parentFieldName]?.length !== formik.values[parentFieldName])
					newState[formKey][parentFieldName] = formik.values[parentFieldName].map((val) => {
						let newVal = {};
						Object.keys(val).forEach((key) => {
							if (typeof val[key] === "object") {
								newVal[key] = val[key].value || "";
							} else {
								newVal[key] = val[key];
							}
						});
						return newVal;
					});
				if (
					newState[formKey] &&
					newState[formKey][parentFieldName] &&
					newState[formKey][parentFieldName][fieldIndex]
				) {
					if (type === "datetime") {
						newState[formKey][parentFieldName][fieldIndex][fieldNameActual] = event || "";
					} else {
						newState[formKey][parentFieldName][fieldIndex][fieldNameActual] =
							event?.target?.value || "";
					}
				}
				return newState;
			} else {
				if (newState[formKey]) newState[formKey][fieldName] = event?.target?.value || "";
				return newState;
			}
		});
		formik.setFieldValue(fieldName, type === "datetime" ? event || "" : event?.target?.value || "");
	};
	const handleDropdownChange = (value, fieldName, formik, formKey, isFieldArray) => {
		setCaseForm((prev) => {
			let newState = { ...prev };
			if (isFieldArray) {
				let [parentFieldName, fieldIndex, fieldNameActual] = fieldName.split(".");
				if (
					newState[formKey] &&
					newState[formKey][parentFieldName] &&
					newState[formKey][parentFieldName][fieldIndex]
				)
					newState[formKey][parentFieldName][fieldIndex][fieldNameActual] =
						value?.value || value || "";
				return newState;
			} else {
				if (newState[formKey]) newState[formKey][fieldName] = value?.value || value || "";
				return newState;
			}
		});
		formik.setFieldValue(fieldName, value);
	};
	const breadcrumbsData = [
		{
			label: t("common:navbar-home"),
			id: "navbar-home",
			link: "/",
			isCurrentPage: false,
		},
		{
			label: t("common:navbar-howToApply"),
			id: "navbar-howToApply",
			link: "/smart-services/how-to-apply",
			isCurrentPage: false,
		},
		{
			label: t("common:applayForSocial"),
			id: "howToApplyForSocialAid",
			link: "#",
			isCurrentPage: true,
		},
	];
	const formKey: string = "socialAidInformation";

	let newBeneficairySections = [
		{
			title: t("socialAidInformation"),
			formKey: "socialAidInformation",
			element: (
				<SocialAidInformationForm
					innerText={t("socialAidInformationSubtext")}
					key="0"
					handleChangeEvent={handleChangeEvent}
					formKey="socialAidInformation"
					initialData={caseForm.socialAidInformation}
					isMaritalInit={caseForm.socialAidInformation.MaritalStatus}
					readOnly={isRequestPending || isFormReadOnly || applyforAllowance}
					handleSetFormikState={handleSetFormikState}
					isMartialEmpty={isMartialEmpty}
					userAge={userDetailsState?.Age}
				/>
			),
		},
		{
			title: t("personalInformation"),
			formKey: "personalInformation",
			element: (
				<PersonalInformation
					innerText={t("personalInformationSubtext")}
					key="1"
					handleChangeEvent={handleChangeEvent}
					formKey={"personalInformation"}
					initialData={caseForm.personalInformation}
					handleSetFormikState={handleSetFormikState}
					isMaritalInit={caseForm.personalInformation.MaritalStatus}
					isMartialEmpty={isMartialEmpty}
					reInitialize={reInitializePeronalForm.current}
					readOnly={isRequestPending || applyforAllowance || isFormReadOnly}
					isEdit={IsEdit}
				/>
			),
		},
		{
			title: t("familyMembersInformation"),
			formKey: "familyMembersInformation",
			element: (
				<FamilyMembersInfoForm
					formKey="familyMembersInformation"
					innerText={t("familyMembersInformationSubtext")}
					key="2"
					familyMembers={familyMembers}
					khulasitQaidNumber={khulasitQaidNumber}
					setFamilyMembers={setFamilyMembers}
					readOnly={isRequestPending || isFormReadOnly || applyforAllowance}
					childMembers={childMembers}
					setChildMembers={setChildMembers}
					caseType={formData?.CaseType ?? 1}
					emiratesId={
						caseForm.personalInformation?.EmiratesID ? caseForm.personalInformation?.EmiratesID : ""
					}
					maritalStatus={caseForm?.personalInformation?.MaritalStatus}
					gender={userDetails?.gender}
				/>
			),
		},
		{
			title: t("housingInformation"),
			formKey: "housingInformation",
			element: (
				<HousingInfoForm
					key="3"
					handleChangeEvent={handleChangeEvent}
					formKey="housingInformation"
					caseData={caseForm}
					initialData={caseForm.housingInformation}
					readOnly={
						isRequestPending ||
						(applyforAllowance && !eligibleHousing) ||
						(IsEdit && eligibleHousing)
					}
					handleSetFormikState={handleSetFormikState}
					IsEdit={IsEdit}
				/>
			),
		},
		{
			title: t("educationInformation"),
			formKey: "educationInformation",
			element: (
				<EducationInfoForm
					key="4"
					formKey="educationInformation"
					members={educationMembers}
					setMembers={setEducationyMembers}
					readOnly={
						isRequestPending ||
						(applyforAllowance && !eligibleEducation) ||
						(IsEdit && eligibleEducation)
					}
					IsEdit={IsEdit}
				/>
			),
		},
		{
			title: t("inflationInformation"),
			formKey: "inflationInformation",
			element: (
				<InflationInfoForm
					key="5"
					handleChangeEvent={handleChangeEvent}
					formKey="inflationInformation"
					initialData={caseForm.inflationInformation}
					readOnly={isRequestPending || isFormReadOnly || applyforAllowance || IsEdit}
					handleSetFormikState={handleSetFormikState}
				/>
			),
		},
	];

	let uploadDocBeneficairySections = [
		{
			title: t("socialAidInformation"),
			formKey: "socialAidInformation",
			element: (
				<SocialAidInformationForm
					innerText={t("socialAidInformationSubtext")}
					key="0"
					handleChangeEvent={handleChangeEvent}
					formKey="socialAidInformation"
					initialData={caseForm.socialAidInformation}
					isMaritalInit={caseForm.socialAidInformation.MaritalStatus}
					readOnly={true}
					handleSetFormikState={handleSetFormikState}
					isMartialEmpty={isMartialEmpty}
					userAge={userDetailsState?.Age}
				/>
			),
		},
		{
			title: t("personalInformation"),
			formKey: "personalInformation",
			element: (
				<PersonalInformation
					innerText={t("personalInformationSubtext")}
					key="1"
					handleChangeEvent={handleChangeEvent}
					formKey="personalInformation"
					isMaritalInit={caseForm.personalInformation.MaritalStatus}
					isMartialEmpty={isMartialEmpty}
					initialData={caseForm.personalInformation}
					handleSetFormikState={handleSetFormikState}
					reInitialize={reInitializePeronalForm.current}
					readOnly={true}
				/>
			),
		},
		{
			title: t("familyMembersInformation"),
			formKey: "familyMembersInformation",
			element: (
				<FamilyMembersInfoForm
					formKey="familyMembersInformation"
					innerText={t("familyMembersInformationSubtext")}
					key="2"
					familyMembers={familyMembers}
					khulasitQaidNumber={khulasitQaidNumber}
					setFamilyMembers={setFamilyMembers}
					readOnly={true}
					childMembers={childMembers}
					setChildMembers={setChildMembers}
				/>
			),
		},
		{
			title: t("housingInformation"),
			formKey: "housingInformation",
			element: (
				<HousingInfoForm
					key="3"
					handleChangeEvent={handleChangeEvent}
					formKey="housingInformation"
					caseData={caseForm}
					initialData={caseForm.housingInformation}
					readOnly={isRequestPending}
					handleSetFormikState={handleSetFormikState}
					IsEdit={IsEdit}
				/>
			),
		},
		{
			title: t("educationInformation"),
			formKey: "educationInformation",
			element: (
				<EducationInfoForm
					key="4"
					formKey="educationInformation"
					members={educationMembers}
					setMembers={setEducationyMembers}
					readOnly={isRequestPending}
					IsEdit={IsEdit}
				/>
			),
		},
		{
			title: t("inflationInformation"),
			formKey: "inflationInformation",
			element: (
				<InflationInfoForm
					key="5"
					handleChangeEvent={handleChangeEvent}
					formKey="inflationInformation"
					initialData={caseForm.inflationInformation}
					readOnly={isRequestPending}
					handleSetFormikState={handleSetFormikState}
				/>
			),
		},
		{
			title: t("incomeInformation"),
			formKey: "incomeInformation",
			element: <IncomeFamilyDetails incomeData={incomeData} readOnly={true} />,
		},
	];

	let sectionsArray = hasSubmitted ? uploadDocBeneficairySections : newBeneficairySections;

	let documentsArray = [
		{
			title: "",
			formKey: "attachedDocuments",
			element: (
				<AttachedDocuments
					innerText=""
					key="0"
					documentList={documentList}
					setDocumentStatus={setDocumentStatus}
				/>
			),
		},
	];

	let reviewArray = [
		{
			title: t("reviewDetails"),
			formKey: "reviewDetails",
			element: (
				<ReviewDocument
					innerText={t("reviewDetailsSubtext")}
					formKey="reviewDetails"
					setCurrentStep={setCurrentStep}
					handleStepsIndexes={_handleStepsIndexes}
					documentList={documentList}
					familyMembers={familyMembers}
					caseForm={caseForm}
					key="0"
					handleSetFormikState={handleSetFormikState}
					hasSSS={hasSSS}
					childMembers={childMembers}
					educationMembers={educationMembers}
				/>
			),
		},
	];

	const maxSteps = sectionsArray.length + documentsArray.length + reviewArray.length;
	const { updateRequest, updateRequestLoading } = useSocialAidRequest(
		caseForm,
		setCaseForm,
		familyMembers,
		setFamilyMembers,
		activeStep,
		activeSubIndex,
		userDetailsState,
		childMembers,
		setChildMembers,
		setKhulasitQaidNumber,
		documentList,
		educationMembers,
		true,
		isCategoryChange.current,
		setEducationyMembers
	);

	let isHousingChange = true;
	let isEducationChange = true;
	const isRequestPendingSubmit = activeStep === 1 && isRequestPending;
	const handleProceed = async (type) => {
		if (!isRequestPending) {
			isCategoryChange.current = false;

			setIsFormReadOnly(true);
			if (activeStep === 0 && activeSubIndex === 0) {
				if (
					caseSavedCategory?.current !== caseForm.socialAidInformation.Category ||
					caseSavedSubCategory?.current !== caseForm.socialAidInformation.SubCategory
				) {
					caseSavedSubCategory.current = caseForm.socialAidInformation.SubCategory;
					caseSavedCategory.current = caseForm.socialAidInformation.Category;
					isCategoryChange.current = true;
				}
			}
			if (activeStep !== 1) {
				if (
					caseForm.socialAidInformation.Category &&
					caseForm.socialAidInformation.SubCategory === BORN_UNKNOWN_PARENTS &&
					caseForm.socialAidInformation.GuardianEmiratesID !== verifiedGuardianEId.current
				) {
					username.current = caseForm.socialAidInformation.GuardianEmiratesID;
					verifyGaurdianPressed.current = true;
					onRequestOtp();
					return;
				}

				//// Prevent user from submissin if there is no change in housing or eduction
				if (applyforAllowance && type === "submit" && activeStep === 2) {
					isHousingChange =
						formData?.CaseDetails?.ApplyHousingAllowance === false &&
						caseForm?.housingInformation?.ApplyHousingAllowance === "no"
							? false
							: isHousingChange;

					isEducationChange = !educationMembers.every(
						(member) =>
							member.ApplyEducationAllowance === false || member.ApplyEducationAllowance === null
					);
				}
				if (!isHousingChange && !isEducationChange) {
					toast({
						title: t("common:noChangesTitle"),
						description: t("common:noChangesDescription"),
						status: "error",
					});
					return;
				}
				// if (type === "submit" && !customerPulseSubmitted) {
				// 	openCustomerPulse();
				// 	return;
				// } else {
				const resp = await updateRequest(type === "submit");
				if (!resp?.IsSuccess) {
					handleApiErrorMessage(resp?.Errors, toast, t, locale);
					setIsFormReadOnly(false);
					return;
				}
				if (activeStep === 0 && activeSubIndex === 1 && !resp?.Data?.CaseDetails?.IsUpdate) {
					//this to indicate the family book is not generated
					return;
				}
				// }
			}

			if (type === "submit") return; // redirect handled by hook
			setIsFormReadOnly(false);
			if ((activeStep === 0 && activeSubIndex === 5) || activeStep === 1) {
				const { data, isError } = await callGetDocumentList();
				if (!data?.IsSuccess === true || isError) {
					toast({
						title: t("common:genericErrorTitle"),
						description: t("common:genericErrorDescription"),
						status: "error",
					});
					return;
				}
			}
		} else {
			if (activeStep === 0 && activeSubIndex === 6) {
				const { data, isError } = await callGetDocumentList();
				if (!data?.IsSuccess === true || isError) {
					toast({
						title: t("common:genericErrorTitle"),
						description: t("common:genericErrorDescription"),
						status: "error",
					});
					return;
				}
			}
			if (activeStep === 1) {
				setIsSubmittingPending(true);
				let docIdList: string[] = [];
				let arr1: any = [];
				let arr2: any = [];
				if (documentList) {
					if (documentList?.ListAdditionalDoc && documentList?.ListAdditionalDoc.length > 0)
						arr1 = documentList?.ListAdditionalDoc?.map((item) => item?.IdDocuments);

					if (documentList?.ListPersonalDocs && documentList?.ListPersonalDocs.length > 0)
						arr2 = documentList?.ListPersonalDocs?.map((item) => item?.IdDocuments);
				}

				docIdList = [...arr1, ...arr2];
				const CaseDetails = {
					ListUploadedDocuments: docIdList,
				};
				const resp = await modifyRequestFromPending(requestId!, CaseDetails);
				if (resp.IsSuccess) {
					router.push(
						`/smart-services/how-to-apply/apply-socialaid/edited-socialaid?requestId=${resp?.Data?.IdCase}`
					);
					return;
				}
				setIsSubmittingPending(false);
				if (!resp?.Data?.IdCase || !resp?.IsSuccess) {
					handleApiErrorMessage(resp?.Errors, toast, t, locale);
					return;
				}
			}
		}
		// ############## un comment this when you want to hide education and housing ################
		// if (activeSubIndex === 2 && IsEdit) {
		// 	if (!eligibleInflation) {
		// 		//Go OUT Inflation (GO to documents directly)
		// 		const { data, isError } = await callGetDocumentList();
		// 		if (!data?.IsSuccess === true || isError) {
		// 			toast({
		// 				title: t("common:genericErrorTitle"),
		// 				description: t("common:genericErrorDescription"),
		// 				status: "error",
		// 			});
		// 			return;
		// 		}
		// 		setActiveSubIndex(6);
		// 		setCurrentStep(6);
		// 	} else if (eligibleInflation) {
		// 		//Go To Inflation
		// 		setActiveSubIndex(5);
		// 		setCurrentStep(5);
		// 	}
		// } else if (activeSubIndex === 2 && !IsEdit) {
		// 	setActiveSubIndex(5);
		// 	setCurrentStep(5);
		// } else {
		// 	setCurrentStep((currentIndex) => {
		// 		if (currentIndex === maxSteps) {
		// 			_handleStepsIndexes(currentIndex);
		// 			return currentIndex;
		// 		}
		// 		_handleStepsIndexes(currentIndex + 1);
		// 		setTimeout(() => {
		// 			window.scrollTo({
		// 				top: 0,
		// 				behavior: "smooth",
		// 			});
		// 		}, 100);
		// 		return currentIndex + 1;
		// 	});
		// }

		// ############## un comment this when you want to show education and housing ################
		if (activeSubIndex === 2 && IsEdit) {
			if (!eligibleHousing) {
				//will go to Housing because user already applied for Housing so he can Edit
				setActiveSubIndex(3);
				setCurrentStep(3);
			} else if (eligibleHousing && !eligibleEducation) {
				//GO To Education
				setActiveSubIndex(4);
				setCurrentStep(4);
			} else if (eligibleHousing && eligibleEducation && !eligibleInflation) {
				//Go OUT Inflation (GO to documents directly)
				const { data, isError } = await callGetDocumentList();
				if (!data?.IsSuccess === true || isError) {
					toast({
						title: t("common:genericErrorTitle"),
						description: t("common:genericErrorDescription"),
						status: "error",
					});
					return;
				}
				setActiveSubIndex(6);
				setCurrentStep(6);
			} else if (eligibleHousing && eligibleEducation && eligibleInflation) {
				//Go To Inflation
				setActiveSubIndex(5);
				setCurrentStep(5);
			}
		} else if (activeSubIndex === 3 && IsEdit) {
			if (!eligibleEducation) {
				//Go education
				setCurrentStep(4);
				setActiveSubIndex(4);
			} else if (eligibleEducation && eligibleInflation) {
				//Go to inflation
				setActiveSubIndex(5);
				setCurrentStep(5);
			} else if (eligibleEducation && !eligibleInflation) {
				//Go OUT  inflation
				const { data, isError } = await callGetDocumentList();
				if (!data?.IsSuccess === true || isError) {
					toast({
						title: t("common:genericErrorTitle"),
						description: t("common:genericErrorDescription"),
						status: "error",
					});
					return;
				}
				setActiveSubIndex(6);
				setCurrentStep(6);
			}
		} else if (activeSubIndex === 4 && IsEdit) {
			if (!eligibleInflation) {
				// Go Documents
				const { data, isError } = await callGetDocumentList();
				if (!data?.IsSuccess === true || isError) {
					toast({
						title: t("common:genericErrorTitle"),
						description: t("common:genericErrorDescription"),
						status: "error",
					});
					return;
				}
				setActiveSubIndex(6);
				setCurrentStep(6);
			} else {
				setCurrentStep((currentIndex) => {
					if (currentIndex === maxSteps) {
						_handleStepsIndexes(currentIndex);
						return currentIndex;
					}
					_handleStepsIndexes(currentIndex + 1);
					setTimeout(() => {
						window.scrollTo({
							top: 0,
							behavior: "smooth",
						});
					}, 100);
					return currentIndex + 1;
				});
			}
		} else {
			console.log("###4");
			setCurrentStep((currentIndex) => {
				if (currentIndex === maxSteps) {
					_handleStepsIndexes(currentIndex);
					return currentIndex;
				}
				_handleStepsIndexes(currentIndex + 1);
				setTimeout(() => {
					window.scrollTo({
						top: 0,
						behavior: "smooth",
					});
				}, 100);
				return currentIndex + 1;
			});
		}
	};

	const handleBack = () => {
		// ############## un comment this when you want to hide education and housing ################
		// if (activeStep === 0 && activeSubIndex === 5 && IsEdit) {
		// 	setCurrentStep(2);
		// 	setActiveSubIndex(2);
		// } else if ((activeStep === 1 || activeSubIndex === 6) && IsEdit) {
		// 	if (eligibleInflation) {
		// 		setCurrentStep(5);
		// 		setActiveSubIndex(5);
		// 		setActiveStep(0);
		// 	} else {
		// 		setCurrentStep(2);
		// 		setActiveSubIndex(2);
		// 		setActiveStep(0);
		// 	}
		// } else if (activeStep === 0 && activeSubIndex === 5 && !IsEdit) {
		// 	setCurrentStep(2);
		// 	setActiveSubIndex(2);
		// } else {
		// 	setCurrentStep((currentIndex) => {
		// 		if (currentIndex === 0) {
		// 			_handleStepsIndexes(currentIndex);
		// 			router.push("/smart-services/how-to-apply");
		// 			return currentIndex;
		// 		}
		// 		_handleStepsIndexes(currentIndex - 1);
		// 		return currentIndex - 1;
		// 	});
		// }

		// ############## un comment this when you want to show education and housing ################
		if (activeStep === 0 && activeSubIndex === 4 && IsEdit) {
			if (eligibleHousing) {
				//Skip Housing
				setActiveSubIndex(2);
				setCurrentStep(2);
			} else if (!eligibleHousing) {
				setActiveSubIndex(3);
				setCurrentStep(3);
			}
		} else if (activeStep === 0 && activeSubIndex === 5 && IsEdit) {
			if (eligibleEducation && eligibleHousing) {
				//Go education
				setCurrentStep(2);
				setActiveSubIndex(2);
			} else if (!eligibleEducation) {
				//Go to inflation
				setActiveSubIndex(4);
				setCurrentStep(4);
			} else if (eligibleEducation) {
				setActiveSubIndex(3);
				setCurrentStep(3);
			}
		} else if ((activeStep === 1 || activeSubIndex === 6) && IsEdit) {
			if (eligibleInflation) {
				setCurrentStep(5);
				setActiveSubIndex(5);
				setActiveStep(0);
			} else if (eligibleEducation && eligibleHousing) {
				setCurrentStep(2);
				setActiveSubIndex(2);
				setActiveStep(0);
			} else if (!eligibleEducation) {
				setCurrentStep(4);
				setActiveSubIndex(4);
				setActiveStep(0);
			} else if (!eligibleHousing) {
				setCurrentStep(3);
				setActiveSubIndex(3);
				setActiveStep(0);
			}
		} else {
			setCurrentStep((currentIndex) => {
				if (currentIndex === 0) {
					_handleStepsIndexes(currentIndex);
					router.push("/smart-services/how-to-apply");
					return currentIndex;
				}
				_handleStepsIndexes(currentIndex - 1);
				return currentIndex - 1;
			});
		}
	};

	function _handleStepsIndexes(currentIndex) {
		debugger
		if (currentIndex < sectionsArray.length) {
			setActiveStep(0);
			setActiveSubIndex(currentIndex);
			// Auto-submission logic for edit cases - similar to inflation service
			if (
				IsEdit &&
				(currentIndex == 1 || currentIndex == 2) &&
				isEditChildCase &&
				isHousingEducationTopup // Only auto-proceed if housingEducationTopup is true
			) {
				console.log("Auto-proceeding to next step in Social Aid form - Housing/Education Topup Edit mode");
				handleProceed("proceed");
			}
		} else if (
			currentIndex >= sectionsArray.length &&
			currentIndex < sectionsArray.length + documentsArray.length
		) {
			setActiveStep(1);
		} else if (currentIndex === maxSteps - 1) {
			setActiveStep(2);
		}
	}

	const handelVerifyAccountError = (data: ICrmAccountData) => {
		if (!data?.AccountType) {
			toast({
				title: t("common:genericErrorTitle"),
				description: t("common:Non-ResidentialError"),
				status: "error",
			});
		} else if (!data?.AccountStatus) {
			toast({
				title: t("common:genericErrorTitle"),
				description: t("common:InactiveError"),
				status: "error",
			});
		} else if (!data?.UAENational) {
			toast({
				title: t("common:genericErrorTitle"),
				description: t("common:Non-EmiratiError"),
				status: "error",
			});
		} else if (data?.ReceivingInflationAllowance) {
			toast({
				title: t("common:genericErrorTitle"),
				description: t("common:ReceivingUtilityAidError"),
				status: "error",
			});
		} else if (!data?.IsValidEID) {
			toast({
				title: t("common:genericErrorTitle"),
				description: t("common:utlityAllowanceError"),
				status: "error",
			});
		}
	};
	// const titles = {
	// 	title: t("requestEditedTitle"),
	// 	caseNumberTitle: t("requestNumber"),
	// 	caseDateTitle: t("forms:complaintSuccessDateTitle"),
	// 	body: t("requestEditedBody1") + t("requestEditedBody2"),
	// 	tableTitle: t("common:applicationSummary"),
	// };
	const onSaveAsDraft = async () => {
		setSaveDraftLoading(true);
		const resp = await updateRequest(false);
		let caseNumber = resp?.Data?.CaseDetails?.CaseRef || caseForm?.personalInformation?.caseID;
		if (resp?.Data?.IdCase) {
			toast({
				title: t("common:draftUpdateSuccess"),
				description: t("common:caseNumberUpdated", { draftId: caseNumber }),
				status: "info",
			});
		} else {
			toast({
				title: t("common:genericErrorTitle"),
				description: t("common:genericErrorDescription"),
				status: "error",
			});
		}
		setSaveDraftLoading(false);
	};

	const proceedButtonLoading =
		!routerReady ||
		updateRequestLoading ||
		isDocumentUploading !== 0 ||
		(((activeStep === 0 && activeSubIndex === 5) ||
			(hasSubmitted && activeStep === 0 && activeSubIndex === 6) ||
			activeStep === 1) &&
			getDocumentListLoading) || // Refetch document list before documents page and review page
		false;
	const showSaveAsDraft = !!query.requestId && !isRequestPending;
	return (
		<Box w="100%">
			<Script src={customerPulseScriptLink} strategy="afterInteractive" />

			<Box>
				<Flex direction={{ base: "column", md: "row" }}>
					<Box w={{ md: "300px", lg: "400px" }} mr={4} display={{ base: "block", md: "block" }}>
						<ProgressTracker
							activeStep={activeStep}
							activeSubIndex={activeSubIndex}
							steps={hasSubmitted ? steps : newBeneficiarySteps}
							service={t("swfProgram")}
						/>
					</Box>

					<Box
						pt={{ base: 0, md: 8 }}
						px={{ base: 0, md: 8 }}
						flexGrow={1}
						bg="white"
						boxShadow="unset"
					>
						<FormContext.Provider value={{ lookups: masterData }}>
							<Box mx={0} mb={4}>
								{/* First Section */}
								{currentStep < sectionsArray.length && (
									<AccordionSocialAid currentStep={currentStep} sectionsArray={sectionsArray} />
								)}
								{/* Second Section */}
								{currentStep >= sectionsArray.length &&
									currentStep < sectionsArray.length + documentsArray.length && (
										<AccordionSocialAid
											currentStep={0}
											sectionsArray={documentsArray}
											hideBottom={true}
										/>
									)}
								{/* Third Section */}
								{currentStep === maxSteps - 1 && (
									<AccordionSocialAid currentStep={0} sectionsArray={reviewArray} />
								)}
							</Box>
						</FormContext.Provider>
						{proceedButtonLoading && activeStep === 0 && activeSubIndex === 1 && (
							<Text textAlign={"center"} color={"green"} textDecoration={"under"}>
								{t("generatingFamilyBook")}
							</Text>
						)}
						{activeStep === 0 && activeSubIndex === 5 && IsEdit && inflationCaseNumber !== "" && (
							<Text textAlign={"center"} color={"red"} textDecoration={"under"}>
								{t("common:inflationEdit", { caseNumber: inflationCaseNumber })}
							</Text>
						)}
						<Flex mt={{ md: "2rem" }} mb="6.5rem" px={6} justifyContent="end">
							{/* <Box display={{ base: "none", md: "block" }}>
								{showSaveAsDraft && (
									<Box mt={2}>
										{!saveDraftLoading && (
											<Flex
												cursor="pointer"
												color="brand.mainGold"
												alignItems="center"
												onClick={onSaveAsDraft}
											>
												<>
													<SaveIcon mr={2} />
													<Text fontWeight="bold" textDecor="underline">
														{t("saveAsDraft", { ns: "common" })}
													</Text>
												</>
											</Flex>
										)}
										{saveDraftLoading && <Spinner color="brand.mainGold" />}
									</Box>
								)}
							</Box> */}
							<Flex
								w={{ base: "100%", md: "auto" }}
								px={{ base: 0, md: "unset" }}
								pb={{ base: 5, md: "unset" }}
								gap={4}
							>
								<Button
									w={{ base: "50%", md: "13.25rem" }}
									variant="secondary"
									isDisabled={currentStep === 0 || (hasSubmitted && currentStep === 6)}
									onClick={handleBack}
								>
									<Text fontSize={{ base: "xs", md: "md" }} as="span">
										{t("back", { ns: "common" })}
									</Text>
								</Button>
								<Button
									ref={submitButton}
									w={{ base: "50%", md: "13.25rem" }}
									variant="primary"
									onClick={async () => {
										if (
											activeStep === 0 &&
											activeSubIndex === 5 &&
											caseForm?.inflationInformation?.ApplyInflationAllowance === "yes" &&
											caseForm?.inflationInformation?.ApplyUtilityAllowance === "yes"
										) {
											let resp = await callValidateAccount();
											if (!resp?.IsSuccess && !resp.Data) {
												handleApiErrorMessage(resp?.Errors, toast, t, locale);
												return;
											} else {
												if (
													childMembers && (resp.Data?.AccountType &&
													resp.Data?.AccountStatus &&
													resp.Data?.UAENational &&
													resp.Data?.ReceivingInflationAllowance &&
													resp.Data?.IsValidEID
												  )) {
													if (resp.Data) {
														toast({
															title: t("common:info"),
															description: t("common:RecevingUtilityInfo"),
															status: "info",
														});
													}
													handleProceed(currentStep < maxSteps - 1 ? "proceed" : "submit");
												  } else if (
													!resp.Data?.AccountType ||
													!resp.Data?.AccountStatus ||
													!resp.Data?.UAENational ||
													resp.Data?.ReceivingInflationAllowance ||
													!resp.Data?.IsValidEID
												) {
													if (resp.Data) handelVerifyAccountError(resp.Data);

													return;
												}
												handleProceed(currentStep < maxSteps - 1 ? "proceed" : "submit");
											}
										} else {
											handleProceed(currentStep < maxSteps - 1 ? "proceed" : "submit");
										}
									}}
									isLoading={
										buttonState?.[stepFormKey]?.isLoading ||
										proceedButtonLoading ||
										customerPulseLoading ||
										isSubmitingPending ||
										generateOtpLoading ||
										signInLoading ||
										isValidatingAccount
									}
									disabled={
										(hasSubmitted ? false : buttonState?.[stepFormKey]?.isDisabled) ||
										buttonState?.[stepFormKey]?.isLoading ||
										proceedButtonLoading ||
										familyMembersStepDisabled ||
										educationStepDisabled ||
										attachDocumentsStepDisabled ||
										customerPulseLoading ||
										isSubmitingPending ||
										generateOtpLoading ||
										signInLoading ||
										isEidExp ||
										!isEmirates ||
										isValidatingAccount
									}
								>
									{/* <Text as="span">
										{t(
											isRequestPendingSubmit
												? "submit"
												: currentStep < maxSteps - 1 || !customerPulseSubmitted
												? "proceed"
												: "submit",
											{
												ns: "common",
											}
										)}
									</Text> */}
									<Text as="span">
										{t(
											isRequestPendingSubmit
												? "submit"
												: currentStep < maxSteps - 1
												? "proceed"
												: "submit",
											{
												ns: "common",
											}
										)}
									</Text>
								</Button>
								{/* {false && (
									<Box mt={2} w="100%" display={{ base: "block", md: "none" }}>
										{!saveDraftLoading && (
											<Flex
												cursor="pointer"
												color="brand.mainGold"
												alignItems="center"
												justifyContent="center"
												onClick={onSaveAsDraft}
											>
												<>
													<SaveIcon mr={2} />
													<Text fontWeight="bold" textDecor="underline">
														{t("saveAsDraft", { ns: "common" })}
													</Text>
												</>
											</Flex>
										)}
										{saveDraftLoading && (
											<Flex>
												<Spinner mx={{ base: "auto", md: "unset" }} color="brand.mainGold" />
											</Flex>
										)}
									</Box>
								)} */}
							</Flex>
						</Flex>
					</Box>
				</Flex>
			</Box>
			<Modal isOpen={isOpen} onClose={onClose} size={"xl"}>
				<ModalOverlay />
				<ModalContent w="fit" maxW="95%">
					<ModalHeader>{t("guardianOtpHeader", { ns: "common" })}</ModalHeader>
					<ModalCloseButton />
					<ModalBody>
						<Box p={8}>
							<ValidateOtp
								otpNumber={otpNumber}
								onEnterOtp={onEnterOtp}
								otpError={otpError}
								onValidateOtp={onValidateOtp}
								onRequestOtp={onRequestOtp}
								onCancel={onCancelOtp}
								signInLoading={signInLoading}
							/>
						</Box>
					</ModalBody>
				</ModalContent>
			</Modal>
		</Box>
	);
}
export async function getServerSideProps(ctx: GetServerSidePropsContext) {
	let IsEdit = ctx.query.editCase === "true";
	// Check if this is a housing/education topup edit
	let isHousingEducationTopup = ctx.query.housingEducationTopup === "true";

	const c = (await BackendServices.getMasterData())?.Data || initialCrmMasterData;
	const masterData = addLocalLookups(c);
	const emiratesId = await getEmiratesIdFromToken(ctx.req);
	const expDate = await getIsEmiratesIDExpiryDateFromToken(ctx.req);
	const isEmiratesData = await getIsEmiratesNationalityFromToken(ctx.req);
	const isEmirates = isEmiratesData != undefined ? isEmiratesData : true;
	const ToDate = new Date();

	let isEidExp = false;
	if (expDate) isEidExp = new Date(expDate).getTime() < ToDate.getTime() ? true : false;
	const requestId = ctx.query.requestId?.toString();
	let profile = await BackendServices.retrieveContact(
		emiratesId,
		requestId ? requestId : "********-0000-0000-0000-********0000"
	);
	let userDetails = profile.Data;
	let hasSubmitted = false;
	let isMartialEmpty =
		profile.Data?.MaritalStatus.MaritalStatusId === "********-0000-0000-0000-********0000";

	let formData: ISocialAidForm | null = null;
	let incomeData: any | null = null;
	let isRequestPending = !!ctx.query.isPending?.toString();
	let guardianEId = "";
	let applyforAllowance = false;
	let eligibleHousing = true;
	let eligibleEducation = true;
	let eligibleInflation = true;
	let inflationCaseNumber = "";
	// let requiredDocumentsList: ICrmDocumentList | null = null;
	const contactId = await getContactIdFromToken(ctx.req);
	let inflationDataCheck = await BackendServices.checkInflationAllowanceEligibility(contactId);
	if (!!requestId) {
		formData = (await BackendServices.getRequest(requestId, contactId))?.Data || null;
		IsEdit = formData?.CaseType === 2 ? true : false;
		if (
			formData?.ParentCaseId &&
			formData?.ParentCaseId !== "********-0000-0000-0000-********0000" &&
			!IsEdit
		) {
			applyforAllowance = true;
		} else if (
			formData?.ParentCaseId &&
			formData?.ParentCaseId !== "********-0000-0000-0000-********0000" &&
			IsEdit
		) {
			applyforAllowance = false;
		}

		eligibleHousing = formData?.eligibleHousing !== undefined ? formData?.eligibleHousing : true;
		eligibleEducation =
			formData?.eligibleEducation !== undefined ? formData?.eligibleEducation : true;

		eligibleInflation =
			formData?.CaseDetails?.ApplyInflationAllowance !== undefined
				? formData?.CaseDetails?.ApplyInflationAllowance
				: true;

		if (inflationDataCheck.IsSuccess && !inflationDataCheck?.Data?.eligibleInflation) {
			//eligibleInflation = inflationDataCheck?.Data?.IdCase === requestId;
			inflationCaseNumber = inflationDataCheck.Data?.IdCase ?? "";
		}

		if (
			formData?.CaseDetails?.GuardianEmiratesID &&
			formData.CaseDetails.SubCategory === BORN_UNKNOWN_PARENTS
		) {
			profile = await BackendServices.retrieveContact(
				formData?.CaseDetails?.GuardianEmiratesID,
				requestId ? requestId : "********-0000-0000-0000-********0000"
			);
			userDetails = profile.Data;
			isMartialEmpty =
				profile.Data?.MaritalStatus.MaritalStatusId === "********-0000-0000-0000-********0000";
			guardianEId = formData?.CaseDetails?.GuardianEmiratesID;
		}
		hasSubmitted = formData?.SubmissionTime ? true : false;
		if ((!formData || !!formData.SubmissionTime) && !isRequestPending) {
			return {
				redirect: {
					destination: `/${ctx.locale || "ar"}/smart-services/how-to-apply/apply-socialaid`,
					permanent: false,
				},
			};
		}
	}

	if (isRequestPending) {
		if (requestId) {
			incomeData = (await BackendServices.getIncome(requestId))?.Data || null;
			let educationData: any = (
				await BackendServices.getRequest(
					requestId.toString(),
					contactId,
					"Request/GetCaseEducationalAllowance"
				)
			)?.Data;
			if (formData && formData.CaseDetails)
				formData.CaseDetails.EducationCaseDetails = educationData?.EducationCaseDetails || null;
		}

		if (
			formData?.IdStatus !==
			masterData.CaseStatus.find((c) => c.Name === "Additional Information Required")?.Id
		) {
			return {
				redirect: {
					destination: `/${ctx.locale || "ar"}/my-cases`,
					permanent: false,
				},
			};
		}
	}
	return {
		props: {
			...(await serverSideTranslations(ctx.locale || "ar", ["common", "tables", "forms", "login"])),
			masterData: getLocalizedLookups(masterData, ctx.locale || "ar"),
			userDetails,
			formData,
			isRequestPending,
			requestId: requestId || null,
			customerPulseScriptLink: CUSTOMER_PULSE_SCRIPT_LINK,
			customerPulseLinkingId: CUSTOMER_PULSE_AID_LINKING_ID,
			isMartialEmpty,
			hasSubmitted,
			incomeData,
			guardianEId,
			isEidExp,
			isEmirates,
			applyforAllowance,
			eligibleHousing,
			eligibleEducation,
			IsEdit,
			eligibleInflation,
			inflationCaseNumber,
			isHousingEducationTopup,
		},
	};
}

ApplySocialAid.getLayout = function getLayout(page: ReactElement) {
	return <MainLayout>{page}</MainLayout>;
};
export default ApplySocialAid;
