import { Box } from "@chakra-ui/react";
import EducationInfoForm from "./EducationInfoForm";

function RequestDetailsForm({ formKey, members, setMembers, readOnly = false, IsEdit }) {
	const onSubmit = async (values: any, actions) => {};
	return (
		<Box>
			<EducationInfoForm
				onSubmit={onSubmit}
				members={members}
				setMembers={setMembers}
				readOnly={readOnly}
				IsEdit={IsEdit}
			/>
		</Box>
	);
}

export default RequestDetailsForm;
