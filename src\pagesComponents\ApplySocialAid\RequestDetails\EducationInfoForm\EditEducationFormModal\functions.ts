import * as Yup from "yup";
import { FormikHelpers } from "formik/dist/types";

const getInitialValues = {
	ApplyEducationAllowance: "",
	IsEnrolledInNationalService: "",
	childCompletedSemesterInUniversity: "",
	highSchoolCurriculuim: "",
	enrolledEducationStream: "",
	EmSATorAdvancedPlacementScores: "",
};

const getValidationSchema = () => {
	return Yup.object({
		ApplyEducationAllowance: Yup.string().required().label("thisField").nullable(),
		childCompletedSemesterInUniversity: Yup.string().when("ApplyEducationAllowance", {
			is: (ApplyEducationAllowance) => {
				return ApplyEducationAllowance === "yes";
			},
			then: Yup.string().required().label("thisField").nullable(),
			otherwise: Yup.string().notRequired().nullable(),
		}),
		IsEnrolledInNationalService: Yup.string().when("ApplyEducationAllowance", {
			is: (ApplyEducationAllowance) => {
				return ApplyEducationAllowance === "yes";
			},
			then: Yup.string().required().label("thisField").nullable(),
			otherwise: Yup.string().notRequired().nullable(),
		}),
		highSchoolCurriculuim: Yup.object().when(
			["ApplyEducationAllowance", "childCompletedSemesterInUniversity"],
			{
				is: (ApplyEducationAllowance, childCompletedSemesterInUniversity) => {
					return ApplyEducationAllowance === "yes" && childCompletedSemesterInUniversity === "no";
				},
				then: Yup.object().shape({
					label: Yup.string(),
					value: Yup.string().required().label("thisField"),
				}),
				otherwise: Yup.object().notRequired().nullable(),
			}
		),
		enrolledEducationStream: Yup.object().when(
			["ApplyEducationAllowance", "childCompletedSemesterInUniversity", "highSchoolCurriculuim"],
			{
				is: (
					ApplyEducationAllowance,
					childCompletedSemesterInUniversity,
					highSchoolCurriculuim
				) => {
					return (
						ApplyEducationAllowance === "yes" &&
						childCompletedSemesterInUniversity === "no" &&
						highSchoolCurriculuim?.value === "662410000"
					);
				},
				then: Yup.object().shape({
					label: Yup.string(),
					value: Yup.string().required().label("thisField"),
				}),
				otherwise: Yup.object().notRequired().nullable(),
			}
		),
		EmSATorAdvancedPlacementScores: Yup.object().when(
			["ApplyEducationAllowance", "childCompletedSemesterInUniversity", "highSchoolCurriculuim"],
			{
				is: (
					ApplyEducationAllowance,
					childCompletedSemesterInUniversity,
					highSchoolCurriculuim
				) => {
					return (
						ApplyEducationAllowance === "yes" &&
						childCompletedSemesterInUniversity === "no" &&
						highSchoolCurriculuim?.value === "662410002"
					);
				},
				then: Yup.object().shape({
					label: Yup.string(),
					value: Yup.string().required().label("thisField"),
				}),
				otherwise: Yup.object().notRequired().nullable(),
			}
		),
	});
};
const onChange = (event: any, formikProps: FormikHelpers<any>) => {
	//console.log("amer", event, formikProps);
};

const getListDefault = (arr) => (!arr || arr?.length === 0 ? [{}] : arr);

export { getInitialValues, onChange, getValidationSchema, getListDefault };
