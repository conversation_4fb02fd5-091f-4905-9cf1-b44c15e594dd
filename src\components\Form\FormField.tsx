import { Text } from "@chakra-ui/react";
import { FormControl, FormErrorMessage, FormLabel } from "@chakra-ui/form-control";
import { useField } from "formik";
import TextInputField from "components/Form/TextInputField";
import Text<PERSON>reaField from "./TextAreaField";
import FileUpload from "components/Form/FileUploadField";
import SelectableTagsField from "./SelectableTagsField";
import { Flex, Tooltip } from "@chakra-ui/react";
import { TooltipIcon } from "components/Icons";
import { useRouter } from "next/router";
import RadioButtonField from "./RadioButtonField";
import CustomRadioButtonField from "./CustomRadioButtonField";

import { useTranslation } from "next-i18next";
import DateTimeInputField from "./DateTimeInputField";
import CustomFormatField from "./CustomFormatField";
import { ViewIcon } from "@chakra-ui/icons";
// import ProposalRadioButtonField from "./ProposalRadioButtonField";

const renderField = (field: any, meta: any, props: any) => {
	switch (props.type) {
		case "Textarea":
			return TextAreaField(field, meta, props);
		case "file":
			return FileUpload(field, meta, props);
		case "selectableTags":
			return SelectableTagsField(field, meta, props);
		case "radio":
			return RadioButtonField(field, meta, props);
		case "CustomRadioButtonField":
			return CustomRadioButtonField(field, meta, props);

		case "datetime":
			return DateTimeInputField(field, meta, props);
		default:
			if (props?.customFormat) return CustomFormatField(field, meta, props);
			return TextInputField(field, meta, props);
	}
};

const FormField = ({
	label,
	isRequired = true,
	tooltip = "",
	subtext = "",
	extraIcon = undefined,
	customText,
	...props
}: any) => {
	const [field, meta] = useField(props);
	const { locale } = useRouter();
	const { t } = useTranslation(["common", "forms"]);

	const handleTranslation = (textToTranslate) => {
		if (
			textToTranslate &&
			typeof textToTranslate === "string" &&
			textToTranslate?.includes("is a required field")
		) {
			let splitData = textToTranslate.split(" ");
			let translatedWord = t(splitData[0], { ns: "forms" });
			return t("isRequired", { ns: "forms", fieldName: translatedWord });
		} else if (textToTranslate && typeof textToTranslate === "object") {
			return t("isRequiredField", { ns: "forms" });
		}
		if (textToTranslate === "forms:spacesnotallowed") {
			return t("spacesnotallowed", { ns: "forms" });
		}
		return t(textToTranslate, { ns: "forms" });
	};

	return (
		<FormControl
			className="form control"
			isRequired={isRequired}
			isInvalid={Boolean(meta.error && meta.touched)}
			position="relative"
		>
			{label && (
				<Flex alignItems={"center"} mb={1.5} gap={1}>
					<Flex flexDir={"column"} justifyContent={"space-between"}>
						<FormLabel
							width="100%"
							h={{ base: "70px", md: "50px" }}
							fontSize={props.labelFontSize || "md"}
							color={props.labelColor || "#1b1d21b8"}
							requiredIndicator={
								<Text as="span" ml="4px">
									*
								</Text>
							}
							m={0}
							{...field}
						>
							{label}
						</FormLabel>

						{customText && (
							<Text color={"#1b1d21b8"} fontSize={"sm"} w="100%" h="50px">
								{t("localSupText2", { ns: "forms" })}
							</Text>
						)}
					</Flex>

					{tooltip && (
						<Tooltip
							hasArrow
							label={tooltip}
							placement={locale === "ar" ? "left" : "right"}
							bg={"brand.gray.400"}
							color={"brand.white.50"}
							fontSize={"xs"}
							fontWeight={"normal"}
							arrowSize={8}
							ms={1}
						>
							<TooltipIcon />
						</Tooltip>
					)}

					{extraIcon && (
						<ViewIcon
							onClick={extraIcon.onClick}
							border={"1px solid "}
							borderColor={"brand.gray.250"}
							rounded="sm"
							padding={1}
							w="6"
							h={"6"}
							cursor={"pointer"}
						/>
					)}
				</Flex>
			)}
			{renderField(field, meta, props)}

			{subtext && !meta.error && (
				<Text fontSize="sm" fontWeight="normal" color="brand.gray.400">
					{subtext}
				</Text>
			)}
			<FormErrorMessage>
				{Array.isArray(meta.error) ? meta.error[0] : handleTranslation(meta.error)}
			</FormErrorMessage>
		</FormControl>
	);
};

export default FormField;
