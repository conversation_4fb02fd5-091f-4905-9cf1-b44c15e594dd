{"root": true, "extends": ["next/core-web-vitals", "plugin:prettier/recommended"], "plugins": ["prettier", "@typescript-eslint", "unused-imports"], "rules": {"linebreak-style": ["error", "unix"], "eqeqeq": "warn", "no-console": "warn", "no-undef": "error", "no-debugger": "error", "prettier/prettier": "error", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/no-explicit-any": "off", "no-unused-vars": "off", "@typescript-eslint/no-unused-vars": "off", "unused-imports/no-unused-imports": "error", "unused-imports/no-unused-vars": ["warn", {"vars": "all", "args": "after-used", "ignoreRestSiblings": false}]}}