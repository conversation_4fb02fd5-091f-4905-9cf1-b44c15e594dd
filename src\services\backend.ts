import { ICrmAllowance } from "interfaces/CrmAllowance.interface";
import { ICrmContact } from "interfaces/CrmContact.interface";
import { ICrmAttachment, ICrmDocumentList } from "interfaces/CrmDocument.interface";
import {
	ICrmLookup,
	ICrmLookupWithParent,
	ICrmMasterData,
} from "interfaces/CrmMasterData.interface";
import { ICrmNotification } from "interfaces/CrmNotification.interface";
import { ICrmRequest } from "interfaces/CrmRequest.interface";
import { ICrmToWhomForm } from "interfaces/CrmToWhom.interface";
import { IGenerateOtpResponse } from "interfaces/GenerateOtp.interface";
import { ISocialAidForm, ISocialAidFormRequest } from "interfaces/SocialAidForm.interface";
import { InflationForm, InflationFormRequest } from "interfaces/InflationForm.interface";
import { IWrapperApiResponse } from "interfaces/WrapperApi.interface";
import { backendApi } from "./api";
import { ICrmComplaint } from "interfaces/CrmComplaint.interface";
import { ComplaintForm } from "interfaces/CrmComplaintForm.interface";
import { IFarmerForm } from "interfaces/FarmerAidForm.interface";
import { ICrmGuardianContact } from "interfaces/CrmGuardianContact.interface";
import { ICrmAppeal } from "interfaces/CrmAppeal.interface";
import {
	AllowanceEligibility,
	InflationEligibilityResponse,
} from "interfaces/CrmAllowanceEligibility.interface";

export const errorResponse = { IsSuccess: false, StatusCode: 500 };

const generateOtp = async (
	emiratesId: string
): Promise<IWrapperApiResponse<IGenerateOtpResponse>> => {
	try {
		const query = {
			emiratesId,
		};
		const { data: resp, request } = await backendApi.get("Authentication/GenerateOTP", {
			params: query,
		});
		return resp;
	} catch (e: any) {
		console.error("im here", e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const validateOtp = async (
	emiratesId: string,
	code: string,
	phoneNumber: string
): Promise<IWrapperApiResponse<ICrmContact | null>> => {
	try {
		const body = {
			EmiratesId: emiratesId,
			Code: code,
			PhoneNumber: phoneNumber,
		};
		const { data: resp } = await backendApi.post("Authentication/ValidateOTP", body);
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const validateGuardian = async (
	emiratesId: string,
	code: string,
	phoneNumber: string
): Promise<IWrapperApiResponse<ICrmGuardianContact | null>> => {
	try {
		const body = {
			EmiratesId: emiratesId,
			Code: code,
			PhoneNumber: phoneNumber,
		};
		const { data: resp } = await backendApi.post("Authentication/ValidateGuardianOTP", body);
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const uaepassPostLogin = async (
	emiratesId: string,
	uuid: string
): Promise<IWrapperApiResponse<ICrmContact | null>> => {
	try {
		const body = {
			EmiratesId: emiratesId,
			UUID: uuid,
		};
		const { data: resp } = await backendApi.post("Contact/UAEPass", body);

		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const retrieveAllRequests = async (
	emiratesId: string
): Promise<IWrapperApiResponse<ICrmRequest[] | null>> => {
	try {
		const query = {
			_emirateId: emiratesId,
		};
		const { data: resp } = await backendApi.post("Request/RetrieveAllRequests", undefined, {
			params: query,
		});
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const retrieveAllowanceTransactions = async (
	emiratesId: string
): Promise<IWrapperApiResponse<ICrmAllowance[] | null>> => {
	try {
		const query = {
			_emirateId: emiratesId,
		};
		const { data: resp } = await backendApi.get("Request/RetrieveAllowanceTransactions", {
			params: query,
		});
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const retrieveContact = async (
	emiratesId: string,
	caseId?: string
): Promise<IWrapperApiResponse<ICrmContact | null>> => {
	try {
		const query = {
			_emirateId: emiratesId,
			_caseId: caseId,
		};
		const { data: resp } = await backendApi.get("Contact/RetrieveContact", {
			params: query,
		});
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const updateProfile = async (
	contactId: string,
	PreferedEmail: string,
	PreferedPhoneNumber: string,
	ProfileImage?: string
): Promise<IWrapperApiResponse<string>> => {
	try {
		const body = {
			PreferedEmail,
			PreferedPhoneNumber,
			ProfileImage,
		};
		const query = {
			id: contactId,
		};
		const { data: resp } = await backendApi.patch("Contact/UpdateProfile", body, {
			params: query,
		});
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const getDocumentList = async (
	idRequest: string
): Promise<IWrapperApiResponse<ICrmDocumentList>> => {
	try {
		const query = {
			idRequest,
		};
		//console.log(query);
		const { data: resp } = await backendApi.post("RequestDocuments/GetAll", undefined, {
			params: query,
		});
		//console.log(resp);
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const getAttachmentById = async (
	idAttachment: string
): Promise<IWrapperApiResponse<ICrmAttachment>> => {
	try {
		const query = {
			idAttachment,
		};
		const { data: resp } = await backendApi.post("RequestDocuments/GetAttachmentId", undefined, {
			params: query,
		});
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const getCaseEditReasonsByCaseId = async (
	caseId: string
): Promise<IWrapperApiResponse<ICrmLookup>> => {
	try {
		const { data: resp } = await backendApi.get("Case/GetCaseUpdateReasons?_caseId=" + caseId);
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const uploadDocument = async (
	idDocument: string,
	listAttachments: ICrmAttachment[]
): Promise<IWrapperApiResponse<boolean>> => {
	try {
		const body = {
			idDocument,
			listAttachments,
		};
		const { data: resp } = await backendApi.post("RequestDocuments/UploadDocument", body);
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const modifyRequest = async ({
	UpdateType,
	reOpenReason,
	IdBeneficiary,
	ParentCaseId,
	IdProcessTempalte,
	IdAllowanceCategory,
	CaseDetails,
	IdCase,
	Index,
	SubIndex,
	CaseType,
}: ISocialAidFormRequest): Promise<IWrapperApiResponse<ISocialAidForm>> => {
	try {
		const body = {
			UpdateType,
			reOpenReason,
			IdCase,
			ParentCaseId,
			CaseDetails,
			IdBeneficiary,
			IdProcessTempalte,
			IdAllowanceCategory,
			Index,
			SubIndex,
			CaseType,
		};
		const { data: resp } = await backendApi.post("Case/ModifyBaseAllowance", body);
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const modifyRequestInflation = async ({
	UpdateType,
	IdBeneficiary,
	ParentCaseId,
	IdProcessTempalte,
	IdAllowanceCategory,
	CaseDetails,
	IdCase,
	Index,
	SubIndex,
}: InflationFormRequest): Promise<IWrapperApiResponse<InflationForm>> => {
	try {
		const body = {
			UpdateType,
			IdCase,
			ParentCaseId,
			CaseDetails,
			IdBeneficiary,
			IdProcessTempalte,
			IdAllowanceCategory,
			Index,
			SubIndex,
		};
		const { data: resp } = await backendApi.post("Case/ModifyBaseAllowance", body);
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const modifyFarmerRequest = async ({
	UpdateType,
	IdBeneficiary,
	IdProcessTempalte,
	IdAllowanceCategory,
	CaseDetails,
	IdCase,
	Index,
	SubIndex,
}: ISocialAidFormRequest): Promise<IWrapperApiResponse<ISocialAidForm>> => {
	try {
		const body = {
			UpdateType,
			IdCase,
			CaseDetails,
			IdBeneficiary,
			IdProcessTempalte,
			IdAllowanceCategory,
			Index,
			SubIndex,
		};
		//console.log("respFarmer", JSON.stringify(body));
		const { data: resp } = await backendApi.post("Case/ModifyFarmerService", body);
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const modifyRequestFromPending = async ({
	UpdateType,
	IdBeneficiary,
	IdProcessTempalte,
	IdAllowanceCategory,
	CaseDetails,
	IdCase,
}: ISocialAidFormRequest): Promise<IWrapperApiResponse<ISocialAidForm>> => {
	try {
		const body = {
			UpdateType,
			IdCase,
			IdBeneficiary,
			CaseDetails,
			IdProcessTempalte,
			IdAllowanceCategory,
		};
		const { data: resp } = await backendApi.post("Case/ModifyBaseAllowance", body);
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const getRequest = async (
	caseId: string,
	beneficiaryId: string,
	endpoint: any = "Request/GetCaseRequestByCaseId",
	isCategoryChange: boolean = false
): Promise<IWrapperApiResponse<ISocialAidForm | IFarmerForm>> => {
	try {
		let query =
			endpoint === "Request/GetCaseFamilyMembersListDetails"
				? { caseId, beneficiaryId, isCategoryChange }
				: { caseId, beneficiaryId };
		const { data: resp } = await backendApi.get(endpoint, {
			params: query,
		});
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

// ***************************************Refund APIS
// To get refund table :
const GetCaseRefundInstallments = async (caseId: string): Promise<IWrapperApiResponse<any>> => {
	try {
		const query = { caseId };
		const { data: result } = await backendApi.get(
			`Request/GetCaseRefundInstallments?caseId=${query.caseId}`
		);

		return result;
	} catch (error: any) {
		console.error("[+]Error in GetCaseRefundInstallments:", error);
		const errorData = error?.response?.data || {};
		return { ...errorResponse, ...errorData };
	}
};

// Pay total amount button
const RefundBreakdown = async (
	CaseId: string,
	BeneficiaryId: string,
	PaymentOption: string
): Promise<IWrapperApiResponse<any>> => {
	try {
		const body = {
			CaseId,
			BeneficiaryId,
			PaymentOption,
		};
		const { data: resp } = await backendApi.post("Refund/RefundBreakdown", body);
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};
const RefundBreakdownSimulator = async (
	CaseId: string,
	BeneficiaryId: string,
	PaymentOption: string
): Promise<IWrapperApiResponse<any>> => {
	try {
		const body = {
			CaseId,
			BeneficiaryId,
			PaymentOption,
		};
		const { data: resp } = await backendApi.post("Refund/RefundBreakdownSimulator", body);
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};
/// Payment for each recored

export const PurchaseRequest = async (
	responseUrl: string,
	errorUrl: string,
	transactions: Array<{ Id: string; Amount: number }>,
	LangId: string
): Promise<IWrapperApiResponse<any>> => {
	try {
		const body = {
			ResponseUrl: responseUrl,
			ErrorUrl: errorUrl,
			TransactionsList: transactions,
			LangId: LangId,
		};

		const { data: resp } = await backendApi.post("Refund/PurchaseRequest", body);
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

// ///////////////

const getIncome = async (
	caseId: string,
	endpoint: any = "Income/GetIncomeDetails"
): Promise<IWrapperApiResponse<any>> => {
	try {
		const query = { caseId };
		const { data: resp } = await backendApi.get(endpoint, {
			params: query,
		});
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const getMasterData = async (): Promise<
	IWrapperApiResponse<ICrmMasterData<ICrmLookup | ICrmLookupWithParent>>
> => {
	try {
		const { data: resp } = await backendApi.get("Master/MasterData");
		// TODO: Remove this once the backend is fixed, lookups names should be the same as when u submit the data
		resp.Data.Area = resp.Data.Areas || [];
		resp.Data.Category = resp.Data.PortalCategory || [];
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const getNotifications = async (
	beneficiaryId: string
): Promise<IWrapperApiResponse<ICrmNotification[]>> => {
	try {
		const query = { beneficiaryId };
		const { data: resp } = await backendApi.get("PortalNotification/GetPortalNofications", {
			params: query,
		});
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const updateNotificationsRead = async (ids: string[]): Promise<IWrapperApiResponse<boolean>> => {
	try {
		const { data: resp } = await backendApi.post(
			"PortalNotification/ChangeNotificationsToRead",
			ids
		);
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const getBeneficiaryDetails = (emiratesId: string) => {
	try {
		const body = {
			EmiratesId: emiratesId,
		};
		backendApi.post("Contact/GetBeneficiaryDetails", body);
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const getFamilyBookDetails = (emiratesId: string) => {
	try {
		const query = {
			EmiratesId: emiratesId,
		};
		backendApi.get("FamilyBook/GetFamilyBookDetails", {
			params: query,
		});
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const createFamilyBookMember = async (
	RequestId: string,
	EmiratesId: string,
	DOB: string
): Promise<IWrapperApiResponse<any>> => {
	try {
		const body = {
			RequestId,
			EmiratesId,
			DOB,
		};
		const { data: resp } = await backendApi.post("FamilyBook/CreateFamilyBookMember", body);
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const createFeedback = async (
	Name: string,
	Email: string,
	Description: string,
	BeneficiaryId: string
): Promise<IWrapperApiResponse<string>> => {
	try {
		const body = {
			Name,
			Email,
			Description,
			BeneficiaryId,
		};
		const { data: resp } = await backendApi.post("Complaint/CreateFeedback", body);
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const toWhomItMayConcernRequest = async ({
	EmiratesId,
	Email,
	MobileNumber,
	EntityAddressed,
	OtherEntity,
	TypeOfCertification,
	OtherEntityAddressed,
}: ICrmToWhomForm): Promise<IWrapperApiResponse<ICrmToWhomForm>> => {
	try {
		const body = {
			EmiratesId,
			Email,
			MobileNumber,
			EntityAddressed,
			OtherEntity,
			TypeOfCertification,
			OtherEntityAddressed,
		};
		const { data: resp } = await backendApi.post("Case/ToWhomItMayConcerns", body);
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

export const ComplaintRequest = async ({
	EmirateId,
	EmailAddress,
	PhoneNumber,
	Title,
	Description,
	CaseType,
	Case,
	listAttachments,
	topicId,
	serviceId,
	subServiceId,
}: ICrmComplaint): Promise<IWrapperApiResponse<ICrmComplaint>> => {
	try {
		let body: any = {
			EmirateId,
			EmailAddress,
			PhoneNumber,
			Title,
			Description,
			CaseType,
			listAttachments,
			topicId,
			Case,
			serviceId,
			subServiceId,
		};
		const { data: resp } = await backendApi.post("Complaint/CreateComplaint", body);
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

export const createAppealRequest = async ({
	Case,
	listAttachments,
	Description,
	Beneficiary,
}: ICrmAppeal): Promise<IWrapperApiResponse<ICrmAppeal>> => {
	try {
		let body: any = {
			Description,
			listAttachments,
			Case,
			Beneficiary,
		};
		const { data: resp } = await backendApi.post("Complaint/CreateAppeal", body);
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const getComplaint = async (
	id: string,
	endpoint: any = "Complaint/GetBeneficiaryComplaints"
): Promise<IWrapperApiResponse<ComplaintForm[]>> => {
	try {
		const query = { id };
		const { data: resp } = await backendApi.get(endpoint, {
			params: query,
		});
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const checkAllowanceEligibility = async (
	EmiratesId: string
): Promise<IWrapperApiResponse<AllowanceEligibility>> => {
	try {
		const query = { EmiratesId };
		const { data: resp } = await backendApi.get("Request/CheckAllowanceEligibility", {
			params: query,
		});
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const createAdditionalDocument = async (
	processDocumentTemplateGUID: string,
	requestGUID: string
): Promise<IWrapperApiResponse<void>> => {
	try {
		const body = {
			processDocumentTemplateGUID,
			requestGUID,
		};
		const { data: resp } = await backendApi.post("RequestDocuments/CreateDocument", body);
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const createPersonalDocumentDoc = async (
	processDocumentTemplateGUID: string,
	requestGUID: string
): Promise<IWrapperApiResponse<void>> => {
	try {
		const body = {
			processDocumentTemplateGUID,
			requestGUID,
		};
		const { data: resp } = await backendApi.post("RequestDocuments/CreateDocumentAdditional", body);
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const deleteAdditionalDocument = async (documentId: string): Promise<IWrapperApiResponse<void>> => {
	try {
		const body = {};
		const { data: resp } = await backendApi.post(
			`RequestDocuments/DeleteDocument?documentId=${documentId}`,
			body
		);
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const appealComplaint = async (ComplaintId: string, reOpenReason: string) => {
	try {
		const { data: resp } = await backendApi.post(
			`Complaint/Appeal?ComplaintId=${ComplaintId}&reOpenReason=${reOpenReason}`
		);
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const validateAccount = async (
	AccountNumber: string,
	Provider: string,
	CaseId: string,
	ParentCaseId: string
) => {
	try {
		let body: any = {
			AccountNumber,
			CaseId,
			ParentCaseId,
		};
		let headers: any = {
			KEY: Provider,
		};
		const { data: resp } = await backendApi.post("Inflation/ValidateAccount", body, {
			headers: headers,
		});
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

const checkInflationAllowanceEligibility = async (
	ContactId: string
): Promise<IWrapperApiResponse<InflationEligibilityResponse>> => {
	try {
		const query = { ContactId };
		const { data: resp } = await backendApi.get("Request/CheckInflationAllowanceEligibility", {
			params: query,
		});
		return resp;
	} catch (e: any) {
		console.error(e);
		const error = e?.response?.data || {};
		return { ...errorResponse, ...error };
	}
};

export const BackendServices = {
	generateOtp,
	validateOtp,
	uaepassPostLogin,
	retrieveAllRequests,
	retrieveAllowanceTransactions,
	retrieveContact,
	updateProfile,
	getDocumentList,
	getAttachmentById,
	uploadDocument,
	getMasterData,
	modifyRequest,
	getRequest,
	GetCaseRefundInstallments,
	RefundBreakdown,
	RefundBreakdownSimulator,
	PurchaseRequest,
	getNotifications,
	updateNotificationsRead,
	getBeneficiaryDetails,
	getFamilyBookDetails,
	createFamilyBookMember,
	getCaseEditReasonsByCaseId,
	createFeedback,
	toWhomItMayConcernRequest,
	ComplaintRequest,
	getComplaint,
	modifyRequestFromPending,
	createAdditionalDocument,
	createPersonalDocumentDoc,
	deleteAdditionalDocument,
	modifyFarmerRequest,
	modifyRequestInflation,
	getIncome,
	appealComplaint,
	validateGuardian,
	createAppealRequest,
	checkAllowanceEligibility,
	validateAccount,
	checkInflationAllowanceEligibility,
};
