{"accomadations": "السكن", "actions": "تحديث", "Action": "الإجراءات", "editReason": "سبب التعديل", "reasonToEdit": "سبب التعديل", "additionalAttachments": "وثائق إضافية", "additionalDocuments": "وثائق إضافية", "additionalDocumentsSubtext": "يرجى تحميل وثائق داعمة إضافية مع طلبك، يمكنك القيام بذلك أدناه.", "addMoreIncome": "أ<PERSON><PERSON> مص<PERSON>ر دخل أخرى", "addPension": "أض<PERSON> المعا<PERSON> التقاعدي", "addRentalIncome": "أض<PERSON> د<PERSON>ل الإيجار", "address": "العنوان (المنطقة ، رقم الشارع ، رقم المنزل)", "addTradeLicense": "أضف رخصة تجارية", "AlternativeEmail": "البريد الإلكتروني المفضل", "alternativeNumber": "رقم الهاتف المفضل", "area": "المنطقة", "attachedDocuments": "المستندات المرفقة", "attachedDocumentsFarmer": "مستندات مرفقة", "attachedDocumentsSubtext": "قم بتحميل المستندات المطلوبة أدناه", "beneficiaryEducationTooltip": "يُرجى تحديد أعلى شهادة أو مؤهل علمي حصلت عليه", "caseID": "رقم الحالة", "caseReason": "سب<PERSON> الحالة", "center": "المركز التابع له", "comment": "الاقتراح / الملاحظة", "companyName": "جهة العمل", "complaintDetails": "ير<PERSON>ى ادخال تفاصيل ال{{inquiryType}}", "complaintSuccessBody": "شكرا لك على تقديم {{inquiryType}}.يمكنك التحقق من حالة ال{{inquiryType}} في أي وقت من خلال صفحة الأستفسارات / الاقتراحات، سنقوم بالتواصل معك لاحقاً في حال احتجنا اي معلومات اضافية", "complaintSuccessBodyForAnonymous": "نشكرك على تقديم {{inquiryType}}. سنتصل بك لاحقًا إذا كنا بحاجة إلى أي معلومات.", "complaintSuccessCaseNumberTitle": "رقم ال{{inquiryType}}", "complaintSuccessDateTitle": "تم التقديم", "complaintSuccessTitle": "تم تقديم {{inquiryType}} بنجاح وهي قيد المراجعة", "complaintTitle": "عنوان ال{{inquiryType}}", "complaintType": "نوع ال{{inquiryType}}", "complete": "مكتمل", "ContractEndDate": "تاريخ نهاية العقد", "ContractNo": "رقم العقد", "ContractStartDate": "تاريخ بداية العقد", "countryOfBirth": "مكان الولادة", "dateOfBirth": "تاريخ الولادة", "deleteCantBeUndone": "لا يمكنك التراجع عن هذا الإجراء.", "deleteFamilyMember": "حذف فرد من العائلة", "deleteFamilyMemberText": "هل أنت متأكد أنك تريد حذف عضو العائلة؟", "deleteIncome": "إلغاء مصدر الدخل", "deleteIncomeText": "هل أنت متأكد أنك تريد إلغاء الدخل الإضافي؟", "deletePension": "إلغاء المعاش التقاعدي", "deletePensionText": "هل أنت متأكد أنك تريد إلغاء المعاش التقاعدي الاضافي؟", "deleteRentalIncome": "إلغاء دخل الإيجار", "deleteRentalIncomeText": "هل أنت متأكد أنك تريد إلغاء دخل الإيجار الإضافي؟", "deleteTradeLicense": "إلغاء الرخصة التجارية", "deleteTradeLicenseText": "هل أنت متأكد أنك تريد إلغاء الرخصة التجارية الاضافية؟", "deletingFile": "<PERSON><PERSON><PERSON> الملف", "docd-ad": "هيئة أبوظبي للدعم الاجتماعي", "DescriptionLimitationMsg": "يجب أن يكون الوصف 700 حرفًا على الأكثر", "docd-dubait": "هيئة تنمية المجتمع - دبي", "InvalidNumberOfChildren": "يجب أن تكون القيمة أكبر من صفر أو اكبر من أو تساوي عدد المعايير المحددة", "PleaseEnterNumbergraterThanZero": "الرجاء إدخال رقم أكبر من الصفر", "documents": "المستندات", "doesFamilyMemberContribute": "هل يساهم هذا الفرد في دخل الأسرة؟", "doesFamilyMemberContributeTooltip": "دخل العمل ، بما في ذلك المدفوعات غير المنتظمة مثل العمولات والمكافآت وما إلى ذلك. يرجى إضافة مصادر دخل إضافية بما في ذلك الدخل من التوظيف الذاتي / العمل الحر / اتفاقيات العقود ، بما في ذلك المدفوعات غير المنتظمة مثل الدخل من المشاريع المستقلة ، إلخ.", "doesFamilyMemberPension": "هل يحصل هذا الفرد من الأسرة على تقاعد أو معاش؟", "doesFamilyMemberPensionTooltip": "الدخل من أنظمة المعاشات التقاعدية (العامة) لجميع أفراد الأسرة بما في ذلك رب الأسرة والمساهمين (مثل الزوجات)", "doesFamilyMemberRental": "هل هذا الفرد من الأسرة لديه دخل إيجار؟", "doesFamilyTradeLicense": "هل هذا الفرد من العائلة لديه رخصة تجارية؟", "dropFile": "أو اسحب الملفات", "editFamilyMembersInformation": "تعديل معلومات أفراد العائلة", "education": "التعليم", "Educations": "المؤهل العلمي", "Emirate": "الإمارة", "Emirates": "الإمارة", "emiratesID": "رقم بطاقة الهوية الإماراتية", "EmiratesID": "رقم بطاقة الهوية الإماراتية", "endDateCantStartBeforeStartDate": "تاريخ النهاية يجب ان يكون بعد تاريخ البداية.", "EnterEmirateIdForEWE": "يرجى ادخال رقم الهوية الإماراتية المسجلة برقم حساب المزرعة من شركة الاتحاد للماء وكهرباء", "enterRequestDetails": "أدخل تفاصيل الطلب", "EntityReceivedFrom": "من أي جهة؟", "EWEBill": "رقم فاتورة من شركة الاتحاد للماء والكهرباء", "eweErrorMessage": "يرجى إدخال رقم فاتورة صحيح المكون من ١٢ رقماً", "familyMemberInformation": "معلومات أفراد الأسرة", "familyMembersInformation": "المعلومات المتعلقة بأفراد الأسرة", "housingInformation": "علاوة بدل السكن", "systemValidation": "التحقق من النظام", "documentGeneration": "إنشاء المستندات", "inflationInformation": "معلومات التضخم", "educationInformation": "علاوة بدل التفوق الدراسي", "familyMembersInformationFarmer": "معلومات أفراد الأسرة", "familyMembersInformationSubtext": "يرجى إدخال جميع المعلومات المطلوبة أدناه. جميع البيانات التي تحمل علامة النجمة (*) إلزامية.", "farmerAidInformation": "المعلومات المتعلقة بدعم أصحاب المزارع", "farmerAidRquestAddedBody2": "طلبك قيد المراجعة سيتم التواصل معك قريباً.", "farmerRequestAdded": "تم تقديم طلبك بنجاح", "filesType": "{{extensions}} نوع الملفات", "fileUploadErrorFileSize": "يرجى تحميل ملف بحجم أقل من {{size}}", "fileUploadErrorFileTypes": "يرجى تحميل ملف بالامتدادات التالية: ‎{{extensions}}", "firstName": "الاسم الأول", "FirstName": "الاسم الأول", "Fullname": "الاسم الكامل", "FullName": "الاسم الكامل", "gender": "الجنس", "healthCardInsurance": "تأمين البطاقة الصحية ساري المفعول", "householdHeadContributes": "هل لدى رب الأسرة دخل مالي من العمل؟", "householdHeadContributesTooltip": "دخل العمل، ويشمل ما تستلمه كمدفوعات مالية غير منتظمة كالعمولات والمكآفات، الخ. يُرجى إضافة مصادر الدخل الإضافي والتي تشمل الدخل من المهن الحرة والعمل الحر (الفريلانس) واتفاقيات التعاقد التي تشمل المدفوعات غير المنتظمة كدخل تستلمه نظير العمل في المشاريع بنظام التعاقد الحر، الخ.", "householdHeadPension": "هل يستلم رب الأسرة دخلاً كمعاش تقاعدي؟", "householdHeadPensionTooltip": "الدخل من أنظمة معاشات التقاعد لجميع أفراد الأسرة بما في ذلك رب الأسرة والمساهمين (مثل الزوجات)", "householdHeadTradeLicense": "هل لدى رب الأسرة رخصة تجارية؟", "householdRentalIncomes": "هل لدى رب الأسرة دخل من إيجار؟", "HowToKnowEWE": "كيفية معرفة رقم الحساب من شركة الاتحاد للماء والكهرباء.", "IDNBackNumber": "رقم بطاقة الهوية الإماراتية (الموجود خلف البطاقة)", "income": "الدخل", "incomeAmount": "قيمة الدخل الشهري (درهم إماراتي)", "Income": "قيمة الدخل الشهري (درهم إماراتي)", "incomeAmountToolTip": "يرجى تحديد مبلغ الدخل لجميع أفراد الأسرة بما في ذلك رب الأسرة والمساهمين (مثل الزوجات)", "incomeInformation": "المعلومات المتعلقة بالدخل", "incomeInformationFarmer": "معلومات الدخل", "incomeInformationSubtext": "يرجى إدخال جميع المعلومات المطلوبة أدناه. جميع البيانات التي تحمل علامة النجمة (*) إلزامية.", "incomeSource": "مصدر الدخل", "IncomeSource": "مصدر الدخل", "IncomeSourceText": "مصدر الدخل", "incomeSourceToolTip": "يرجى تحديد نوع مصدر الدخل لجميع أفراد الأسرة بما في ذلك رب الأسرة والمساهمين (مثل الزوجات)", "IncomeTypes": "مصدر الدخل", "informationRequired": "المعلومات مطلوبة", "isRequired": "{{fieldName}} هو حقل إلزامي.", "PleaseEntera1or2-digit": "الرجاء إدخال رقم مكون من خانة او خانتين", "PleaseEnteraPositivenumber": " هذه الخانة يجب أن تحتوي على رقم صحيح أكبر من أو يساوي 1", "PleaseEnteranIntegerNumber": "الرجاء إدخال رقم صحيح", "isIntegerAndhasOneOrTwoDigits": "هذه الخانة يجب أن تحتوي على رقم صحيح فقط", "NumberofPoDSiblings": "يجب أن يكون عدد الأشقاء من اصحاب الهمم أقل من أو يساوي عدد الأشقاء", "NumberofPoDChildren": "يجب أن يكون عدد الأطفال من اصحاب الهمم أقل من أو يساوي عدد الأطفال الإماراتيين", "PleaseEnterNumberBiggerThanZero": "الرجاء يدخل رقم أكبر من الصفر", "ThisFieldShouldbeNumber": "هذه الخانة يجب أن تحتوي على رقم صحيح فقط", "lessThanOrEqual4": " يجب ان يكون هذا الرقم اقل من او يساوي 4 ", "numberOfPODSpouses": "عد<PERSON> الزوجات من اصحاب الهمم", "numberOfChildren": "ع<PERSON><PERSON> الأط<PERSON>ال الاماراتيين", "totalIncome": "إجمالي الدخل", "numberOfSpouses": "<PERSON><PERSON><PERSON> الزوجات", "jobTitle": "المسمى الوظيفي", "lastName": "اسم العائلة", "LastName": "اسم العائلة", "last": "الاسم الكامل", "localNumberFormatSubtext": "رقم هاتف إم<PERSON>اتي", "MaritalStatus": "الحالة الاجتماعية", "maxSize": "الح<PERSON> الأقصى {{size}}", "memberName": "اسم الفرد", "mocd": "وزارة تمكين المجتمع", "mustBeNumber": "هذا الحقل يجب ان يكون رقماً.", "mustBePositive": "عُذراً، يجب أن تكون قيمة المبلغ أكبر من 0.", "next": "التالي", "noDocumentsUpload": "لا توجد مستندات مطلوبة ، يرجى المتابعة إلى الخطوة التالية.", "noEWENoFarmerDesc": " هذه الخدمة مخصصة للمسجلين في شركة الاتحاد للماء و الكهرباء", "noEWENoFarmerTitle": "لا يمكنك التقديم لهذه الخدمة", "noFamilyMembersData": "لا توجد بيانات متاحة لأفراد العائلة ، يرجى المتابعة إلى الخطوة التالية.", "noEducationMembersData": "لا يوجدأبناء فوق ال ١٦ عام ، يرجى المتابعة إلى الخطوة التالية.", "numberOfHousehold": "<PERSON><PERSON><PERSON> أفراد الأسرة", "Occupations": "الحالة الوظيفية", "Verify Emirates ID": "التحقق من هوية الإمارات", "ownerEWEBill": "هل أنت مالك حساب المزرعة من شركة الاتحاد للماء والكهرباء", "passportCopy": "صورة عن جواز السفر", "passportNo": "رقم جواز السفر.", "PassportNumber": "رقم جواز السفر", "pension": "معاش التقاعد", "pensionAmount": "مبلغ معاش التقاعد الشهري (بالدرهم الإماراتي)", "PensiontAuthority": "هيئة التقاعد", "PensiontAuthorityText": "هيئة التقاعد", "PensionType": "نوع معاش التقاعد", "PensionAuthority": "هيئة المعاشات التقاعدية", "personalDocuments": "مستندات شخصية", "personalInformation": "معلومات شخصية", "EditReason": "تعديل السبب", "personalInformationSubtext": "يرجى إدخال جميع المعلومات المطلوبة أدناه. جميع البيانات التي تحمل علامة النجمة (*) إلزامية.", "phoneNumber": "رقم الهاتف", "PleaseEnterEWENumber": "ير<PERSON>ى إدخال رقم حساب المزرعة من شركة الاتحاد للماء والكهرباء", "pleaseEnterValidEid": "يرجى ادخال رقم هوية إماراتية فعالة", "PreferredEmail": "الب<PERSON>يد الإلكتروني", "PreferredEmailUsed": " البريد الإلكتروني (سيتم إرسال الشهادة إلى البريد الالكتروني أدناه)", "PreferredPhoneNumber": "رقم الهاتف", "ReceiveInflationAllowance": "هل تتلقى علاوة تضخم؟", "ReceiveSocialAid": "هل تتلقى مساعدات اجتماعية؟", "RegisteredWithEWE": "هل أنت مسجل لدى شركة الاتحاد للماء والكهرباء؟", "RelatedEmiratesID": "رقم بطاقة الهوية الإماراتية المسجل برقم الحساب من شركة الاتحاد مياه وكهرباء", "relationship": "العلاقة", "RentalIncome": "دخل الإيجار", "RentalIncomes": "دخل الإيجار", "rentalSource": "مصدر الإيجار", "RentalSource": "مصدر الإيجار", "RentalSourceText": "مصدر الإيجار", "RentAmount": "مقدار الإيجار الشهري (درهم إماراتي)", "requestAddedBody1": "شكرا لك على تقديم طلب للحصول على خدمة المساعدة الاجتماعية.", "requestAddedBody1Inflation": "شكرا لك على تقديم طلب للحصول على برنامج التضخم الاقتصادي .", "requestAddedBody1Farmer": " شكراً على تقديم طلب الحصول على دعم استهلاك الكهرباء لمالكي المزارع. ", "requestAddedBody2": "تم تقديم طلبك للخدمة وهو قيد المراجعة. ستتواصل وزارة تمكين المجتمع قريبا", "requestAddedTitle": "تم تقديم طلبك بنجاح وهو قيد المراجعة", "requestEditedBody1": "شكرا لك على تعديل طلب للحصول على خدمة المساعدة الاجتماعية.", "requestEditedBody1Farmer": "شكرا لك على تعديل طلب للحصول على دعم أصحاب المزارع.", "requestEditedBody2": "تم تعديل طلبك للخدمة وهو قيد المراجعة. ستتواصل وزارة تمكين المجتمع قريبا", "requestEditedTitle": "تم تعديل طلبك بنجاح وهو قيد المراجعة", "requestNumber": "رق<PERSON> الطلب", "requestSubmitted": "تم تقديم الطلب", "reviewDetails": "مراجعة البيانات", "reviewDetailsFarmer": "مراجعة التفاصيل", "reviewDetailsSubtext": "يرجى مراجعة المعلومات والمتابعة كما هو مطلوب.", "selectFiles": "اختر الملف", "socialAidInformation": "المعلومات المتعلقة بالدعم الاجتماعي", "socialAidInformationFarmer": "معلومات المساعدة الاجتماعية", "socialAidInformationSubtext": "يرجى إدخال جميع المعلومات المطلوبة أدناه. جميع البيانات التي تحمل علامة النجمة (*) إلزامية.", "socialServices-sharjah": "دائرة الخدمات الاجتماعية - الشارقة", "status": "الحالة", "StatusCode": "الحالة", "submitRequest": "تقديم الطلب", "submittedOn": "Submitted On", "thankYouForFeedback": "شكرًا لك على ملاحظاتك.", "thisField": "هذا الحقل", "tradeLicense": "الرخصة التجارية", "tradeLicenseAmount": "مبلغ الرخصة التجارية الشهري (بالدرهم الإماراتي)", "TradeSourceText": "مصدر الرخصة", "uaeMobileNumberError": "ير<PERSON>ى إدخال رقم هاتف بالصيغة التالية ‎05XXXXXXXX", "uaeIDNumberError": "الرجاء إدخال رقم إماراتي صحيح", "uaeResidenceVisa": "تأشيرة الإقامة الإماراتية", "universityDegree": "شهادة معتمدة من الجامعة", "uploadingFile": "تحميل الملف", "useLengthError": "الرجاء ادخال رقم مكون من ١٢", "validWorkContractFamily": "عقد عمل ساري المفعول", "wrongEmailAddress": "عُذراً، يُرجى إدخال بريد إلكتروني صحيح.", "Area": "المنطقة", "Category": "السبب الرئيسي لطلب المساعدة", "SubCategory": "السبب الفرعي", "EmiratesResd": "إمارة السكن", "Center": "المركز التابع له", "youHaveToReadTerms": "يجب عليك قراءة الإقرار والتعهد", "addMoreDocs": "إضافة مستندات أُخرى", "addingMoreDocuments": "إضافة مستندات أُخرى", "IsDraftedinMilitaryService": "تجند في الخدمة العسكرية منذ أن بلغ من العمر 21 عامًا", "IsPursuingHigherEducation": "متابعة التعليم العالي (أعلى من الثانوية) منذ بلوغ سن 21 عامًا", "IsActiveStudent": "هل أنت مستمر بالدراسة ؟", "MilitaryServiceStatus": "الخدمة الوطنية", "Terminated": "الوظيفة السابقة", "jobseekerErrorMsg": "هذه المساعدة مؤقتة لمدة ستة أشهر– وبإمكانك التقدم لها مرتان فقط خلال الـ خمس سنوات القادمة", "notApplicableErrorMsg": "لا يمكنك تحديد قيمة أخرى مع خيار غير قابلة للتطبيق", "feedbackRecived": "تم إستلام ملاحظتك، شكراً لك.", "swfProgram": "برنامج الدعم الاجتماعي", "InflationProgram": "برنامج التضخم الاقتصادي ", "HaveChildrenCustody": "هل لديك حضانه لأطفالك ؟", "ReceivedLocalSupport": "هل تتلقى أنت أو زوجتك أي دعم اجتماعي محلي آخر؟", "guardianIncome": "دخل ولي الأمر (الدرهم الإماراتي)", "PursuingHigherEducation": "هل تابعت التعليم العالي منذ أن بلغت 21 عامًا؟", "PursuingMilitaryService": "هل تابعت الخدمة العسكرية منذ أن كان عمرك 21 عامًا؟", "GuardianEmiratesID": "رقم بطاقة الهوية الإماراتية للوصي", "PensiontAmount": "مبلغ المعاش الشهري (بالدرهم)", "noDataFound": "لايوجد بيانات", "theService": "الخدمة", "completeInfo": "اكمل المعلومات", "selectDocType": "اختر نوع الملف الذي تريد تحميله", "previous": "السابق", "Topic": "الموضوع", "FamilyHousholdName": "أسم رب الأسرة:", "khulasitQaidNumber": "رقم خلاصة القيد", "ChildEligibilityforWomeninDifficulty": "هل لديك حضانة طفل واحد على الأقل يستوفي المعايير التالية؟", "select": "ير<PERSON>ى التحديد إن أمكن", "isRequiredField": "هذا حقل إلزامي.", "NumberOfChildren": "كم عدد الأطفال الذين يستوفون المعايير المذكورة أعلاه؟", "NumberOfChildrenLessThan25": "كم عدد الأطفال الذين أعمارهم أقل من 25؟", "complaintServices": "خدمة", "complaintSubServices": "خدمة فرعية", "generatingFamilyBook": "يرجى الانتظار… يتم استرجاع خلاصة القيد", "thisFieldshouldbeLess": "يجب أن تكون القيمه أكبر من صفر أو أقل أو تساوي عدد الأطفال الذين يستوفون الشروط أعلاه", "greaterThanZero": "يجب أن تكون القيمه أكبر من صفر", "localSupText": "(من أي من هذه الجهات التالية: هيئة أبوظبي للدعم الاجتماعي، هيئة تنمية المجتمع، دائرة الخدمات الاجتماعية بالشارقة)", "localSupText2": "(برنامج الشيخ زايد للإسكان، هيئة أبوظبي للإسكان، مؤسسة محمد بن راشد للإسكان، دائرة الإسكان في الشارقة)", "Verified": "تم التحقق", "PendingVerification": "في انتظار التحقق", "complaintHeader": "عنوان الشكوى", "inquiryHeader": "عنوان الاستفسار", "suggestionHeader": "عنوان الاقتراح", "thankYouTitleHeader": "عنوان الشكر", "thankYouTitle": "شكر", "numberOfSiblingsError": "الرجاء إدخال رقم مكون من رقمين لعدد الأشقاء", "ApplyHousingAllowance": "هل ترغب في تقديم طلب للحصول على علاوة بدل السكن؟", "IsHouseholOwnerResidentialProperty": "<PERSON><PERSON><PERSON> أفراد الأسرة هو المالك الوحيد للعقار السكني المبني", "ReceivingFederalLocalhousingsupport": "هل تتلقى أنت أو أي من أفراد أسرتك دعم سكني من أي من البرامج الاتحادية أو المحلية التالية؟", "ReceivingHousingAllowanceFromEmployer": "هل تحصل أنت أو أي من أفراد أسرتك العاملين على علاوات بدل السكن من رب العمل؟ (لا تشمل زيادة بدل السكن التي تكون جزء من الراتب الشهري)", "IsUtilityBillIssuedForFullyOwnedProperty": "هل تم إصدار فاتورة خدمات (ماء/كهرباء) للعقار السكني المملوك بالكامل؟", "FullOwnershipResidentialProperty": "هل تملك أنت أم أي من افراد الأسرة عقارات سكنية بشكل كلي أو جزئي؟", "LivingSituation": "ما هو وضع المعيشة الخاص بك؟", "ApplyEducationAllowance": "هل تريد التقدم بطلب للحصول على علاوة التفوق الدراسي لهذا الابن/الشقيق؟", "childCompletedSemesterInUniversity": "هل أكمل هذا الابن/الشقيق أكثر من فصل دراسي واحد في جامعة معتمدة؟", "highSchoolCurriculuim": "يرجى تحديد منهاج التعليم الثانوي", "enrolledEducationStream": "ما هو مسار التعليم العام الذي التحق به الطفل/الأخ؟", "EmSATorAdvancedPlacementScores": "هل ترغب في تحميل درجات اختبار (EmSAT) أو تحديد المستوى المتقدم؟", "Age": "العمر", "ApplyEducation": "تقدم بطلب للحصول على علاوة التفوق الدراسي", "applyedtrue": "نعم", "applyedfalse": "لا", "ReceivesHousingSupportFromHusband": "هل تحصلين على دعم سكني من طليقكِ بموجب حكم المحكمة؟", "ApplyInflationAllowance": "هل ترغب في تقديم طلب للحصول على علاوة بدل التضخم؟", "ApplyUtilityAllowance": "هل ترغب في تقديم طلب للحصول على علاوة بدل الكهرباء والمياه أيضًا؟", "UtilityProvider": "يرجى تحديد شركة الكهرباء/المياه", "UtilityAccountNumber": "ير<PERSON>ى إدخال رقم حساب الخدمات الذي ترغب في حصوله على بدل علاوة الكهرباء والمياه", "IsEnrolledInNationalService": "هل الطفل ملتحق حالياً بالخدمة الوطنية؟", "complaint": "شكوى", "inquiry": "استفسار", "suggestion": "اقتراح", "informationForm": "سب<PERSON> التقديم", "InflationCategory": "سب<PERSON> التقديم", "discliamer": " يمكن تقديم الطلب للحصول على علاوة التفوق الدراسي عند إتمام الخدمة الوطنية ", "disabledFieldMessage": "يمكنك تحديث الهاتف المفضل أو البريد الإلكتروني المفضل ضمن ملفي الشخصي", "womanOver45": "يرجى إرفاق هويات المحضونين وإثبات الحضانة إن وجد", "spacesnotallowed": "هذا الحقل هو حقل إلزامي."}