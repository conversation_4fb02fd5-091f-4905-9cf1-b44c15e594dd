import { AttachmentIcon } from "@chakra-ui/icons";
import {
	Link,
	Table,
	TableCaption,
	TableContainer,
	Tbody,
	Td,
	Th,
	Thead,
	Tr,
	Text,
	Show,
	VStack,
	Flex,
} from "@chakra-ui/react";
import { SPOUSE_INCAPACITATED_FOREIGNER } from "config";
import useDownloadAttachment from "hooks/useDownloadAttachment";
import { useTranslation } from "react-i18next";

function SocialAidStripedTable({
	caption,
	tableHeader,
	tableBody,
	mb = 10,
	sourceOfTranslation = "tables",
	SubcatValue = "",
}: any) {
	const { t } = useTranslation(["tables", "forms"]);
	const downloadAttachment = useDownloadAttachment();
	return (
		<>
			<Show above="md">
				<TableContainer
					mb={mb}
					border="1px"
					borderBottom="0px"
					borderColor="brand.tableBorderColor"
					rounded="lg"
				>
					<Table variant="simple">
						{caption && <TableCaption>{caption}123</TableCaption>}
						<Thead>
							<Tr fontWeight="medium" mb={4}>
								{tableHeader && (
									<Th
										color="brand.tableTextColor"
										textTransform="none"
										lineHeight="150%"
										fontSize="xl"
										fontWeight="bold"
										letterSpacing="unset"
										pt={3}
										pb={3.5}
										px={4}
										colSpan={2}
										borderColor="brand.tableBorderColor"
									>
										{tableHeader}
									</Th>
								)}
							</Tr>
						</Thead>
						<Tbody>
							{tableBody &&
								tableBody.map((row, rowIdx) => {
									if (row.value === "-") return;
									if (row.value === "00000000-0000-0000-0000-000000000000") return;

									return (
										<Tr key={rowIdx} display={row?.hide === true ? "none" : "table-rows"}>
											<Td
												borderColor="brand.tableBorderColor"
												fontSize="1rem"
												fontWeight="bold"
												lineHeight="150%"
												letterSpacing="unset"
												w="50%"
											>
												{SubcatValue === SPOUSE_INCAPACITATED_FOREIGNER &&
												row.label === "ChildEligibilityforWomeninDifficulty"
													? t("select", { ns: "forms" })
													: `${t(row.label, { ns: sourceOfTranslation })}${
															row.index ? ` (${row.index})` : ""
													  }`}
											</Td>
											<Td
												borderColor="brand.tableBorderColor"
												fontSize="0.875rem"
												fontWeight="normal"
												letterSpacing="unset"
												lineHeight="150%"
											>
												<Text dir={"auto"} w={"min-content"}>
													{!row.document && row.value && row.value.length < 50
														? row.value
														: !row.document &&
														  row.value &&
														  typeof row.value === "string" && (
																<span title={row.value}>{`${row.value.slice(0, 80)}...`}</span>
														  )}

													{row.document && (
														<Link
															color={"brand.blue.300"}
															onClick={() => downloadAttachment(row.document?.Id)}
															title={row.value}
														>
															{row.value.length > 20 ? `${row.value.slice(0, 80)}...` : row.value}
															<AttachmentIcon mx={2} transform={"rotate(135deg)"} />
														</Link>
													)}
												</Text>
											</Td>
										</Tr>
									);
								})}
						</Tbody>
					</Table>
				</TableContainer>
			</Show>
			<Show below="md">
				<VStack align={"start"}>
					{tableBody.map((row, index) => {
						if (row.value === "-") return;
						return (
							<Flex
								key={index}
								display={row?.hide === true ? "none" : "flex"}
								w="full"
								p={4}
								justifyContent={"space-between"}
								borderBottom="1px solid #BBBCBD"
							>
								<VStack flex={2} align={"start"}>
									<Text>
										{SubcatValue === SPOUSE_INCAPACITATED_FOREIGNER &&
										row.label === "ChildEligibilityforWomeninDifficulty"
											? t("select", { ns: "forms" })
											: `${t(row.label, { ns: sourceOfTranslation })}${
													row.index ? ` (${row.index})` : ""
											  }`}
									</Text>
									<Text dir={"auto"}>
										{!row.document && row.value}
										{row.document && (
											<Link
												color={"brand.blue.300"}
												onClick={() => downloadAttachment(row.document?.Id)}
											>
												{row.value}
												<AttachmentIcon mx={2} transform={"rotate(135deg)"} />
											</Link>
										)}
									</Text>
								</VStack>
							</Flex>
						);
					})}
				</VStack>
			</Show>
		</>
	);
}

export default SocialAidStripedTable;
