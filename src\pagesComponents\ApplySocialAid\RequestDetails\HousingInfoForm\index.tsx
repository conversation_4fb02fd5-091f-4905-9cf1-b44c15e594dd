import { Box } from "@chakra-ui/react";
import HousingInfoForm from "./HousingInfoForm";

function RequestDetailsForm({
	handleChangeEvent,
	formKey,
	caseData,
	handleSetFormikState,
	initialData,
	readOnly = false,
	IsEdit,
}) {
	const onSubmit = async (values: any, actions) => {};
	return (
		<Box>
			<HousingInfoForm
				onSubmit={onSubmit}
				handleChangeEvent={handleChangeEvent}
				formKey={formKey}
				caseData={caseData}
				handleSetFormikState={handleSetFormikState}
				initialData={initialData}
				readOnly={readOnly}
				IsEdit={IsEdit}
			/>
		</Box>
	);
}

export default RequestDetailsForm;
