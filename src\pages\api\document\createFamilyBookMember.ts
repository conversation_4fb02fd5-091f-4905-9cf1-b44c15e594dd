import { IWrapperApiResponse } from "interfaces/WrapperApi.interface";
import type { NextApiRequest, NextApiResponse } from "next";
import { errorResponse, BackendServices } from "services/backend";

export default async function handler(
	req: NextApiRequest,
	res: NextApiResponse<IWrapperApiResponse<void>>
) {
	const { RequestId, EmiratesId, DOB } = req.body;
	if (!RequestId || !EmiratesId || !DOB)
		return res.status(400).json({ ...errorResponse, Errors: "Required fields missing" });

	const data = await BackendServices.createFamilyBookMember(RequestId, EmiratesId, DOB);

	if (data) {
		res.status(200).json(data);
	} else {
		res.status(500).json(errorResponse);
	}
}

export const config = {
	api: {
		bodyParser: {
			sizeLimit: "10mb",
		},
	},
};
