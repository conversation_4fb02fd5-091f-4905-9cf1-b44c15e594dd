import { Radio, RadioGroup, Stack } from "@chakra-ui/react";

const RadioButtonField = (field: any, meta: any, props: any) => {
	return (
		<RadioGroup value={props.value} {...field} {...props}>
			<Stack spacing={6} direction="column">
				{props.options.map((value, idx) => (
					<Radio
						outline="1px solid #1b1d2152"
						borderColor="brand.white.50"
						key={idx}
						{...field}
						value={value.value}
						_checked={{
							bg: "#A0813E",
							color: "white",
							border: "2px solid #B68A35",
							borderColor: "white",
							outline: "1px solid #B68A35",
						}}
						_focus={{
							boxShadow: "none",
						}}
						isReadOnly={props.isReadOnly}
						_readOnly={{
							_checked: {
								bg: "#B68A35",
								opacity: 0.6,
							},
						}}
						readOnly={props.isReadOnly}
					>
						{value.label}
					</Radio>
				))}
			</Stack>
		</RadioGroup>
	);
};
export default RadioButtonField;
